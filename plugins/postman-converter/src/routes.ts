import { createRouteRef, createSubRouteRef } from '@backstage/core-plugin-api';

export const rootRouteRef = createRouteRef({
  id: 'postman-converter',
});

export const collectionRouteRef = createSubRouteRef({
  id: 'collection',
  parent: rootRouteRef,
  path: '/:id',
});

export const newCollectionRouteRef = createSubRouteRef({
  id: 'new-collection',
  parent: rootRouteRef,
  path: '/new',
});

export const editCollectionRouteRef = createSubRouteRef({
  id: 'edit-collection',
  parent: rootRouteRef,
  path: '/:id/edit',
});
