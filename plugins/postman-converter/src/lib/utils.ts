/**
 * Utility functions for the postman-converter plugin
 */

/**
 * Converts a string to camelCase
 * 
 * @param str - The string to convert to camelCase
 * @returns The camelCase version of the string
 */
export function camelCase(str: string): string {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '')
    .replace(/[-_]+/g, '');
}
