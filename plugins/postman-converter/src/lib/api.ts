/**
 * API utilities for K6 tests
 */

import http from 'k6/http';
import { CustomHeaders } from './types';

/**
 * API helper for making HTTP requests with consistent handling
 */
export const api = {
  /**
   * Make a GET request
   */
  get: (url: string, name: string, headers: CustomHeaders) => {
    return http.get(url, {
      headers,
      tags: { name },
    });
  },

  /**
   * Make a POST request
   */
  post: (url: string, body: any, name: string, headers: CustomHeaders) => {
    return http.post(url, JSON.stringify(body), {
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      tags: { name },
    });
  },

  /**
   * Make a PUT request
   */
  put: (url: string, body: any, name: string, headers: CustomHeaders) => {
    return http.put(url, JSON.stringify(body), {
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      tags: { name },
    });
  },

  /**
   * Make a PATCH request
   */
  patch: (url: string, body: any, name: string, headers: CustomHeaders) => {
    return http.patch(url, JSON.stringify(body), {
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      tags: { name },
    });
  },

  /**
   * Make a DELETE request
   */
  delete: (url: string, name: string, headers: CustomHeaders) => {
    return http.del(url, null, {
      headers,
      tags: { name },
    });
  },

  /**
   * Make a request with path parameters
   * Replaces :paramName in the URL with the corresponding value from params
   */
  requestWithParams: (method: string, url: string, params: Record<string, string>, body: any = null, name: string, headers: CustomHeaders) => {
    // Replace path parameters
    let finalUrl = url;
    Object.entries(params).forEach(([key, value]) => {
      finalUrl = finalUrl.replace(`:${key}`, value);
    });

    // Make the request based on the method
    switch (method.toLowerCase()) {
      case 'get':
        return api.get(finalUrl, name, headers);
      case 'post':
        return api.post(finalUrl, body, name, headers);
      case 'put':
        return api.put(finalUrl, body, name, headers);
      case 'patch':
        return api.patch(finalUrl, body, name, headers);
      case 'delete':
        return api.delete(finalUrl, name, headers);
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }
};
