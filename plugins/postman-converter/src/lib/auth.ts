/**
 * Authentication utilities for K6 tests
 */

import http from 'k6/http';
import { check } from 'k6';
import { CustomHeaders } from './types';

/**
 * Simulates a login and returns a token
 * This is a placeholder implementation that should be customized for your API
 */
export function login(): string {
  const loginUrl = __ENV.LOGIN_URL || 'https://api.example.com/login';
  const username = __ENV.USERNAME || 'testuser';
  const password = __ENV.PASSWORD || 'testpassword';

  const payload = JSON.stringify({
    username,
    password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const res = http.post(loginUrl, payload, params);
  
  check(res, {
    'login successful': (r) => r.status === 200,
  });

  try {
    // Parse the response to get the token
    const body = JSON.parse(res.body as string);
    return body.token || 'dummy-token-for-testing';
  } catch (e) {
    console.log('Failed to parse login response:', e);
    return 'dummy-token-for-testing';
  }
}

/**
 * Simulates calling the homepage or a main endpoint
 * This is useful for initial navigation in a test flow
 */
export function callHomepage(customHeaders: CustomHeaders): void {
  const homepageUrl = __ENV.HOMEPAGE_URL || 'https://api.example.com/';
  
  const res = http.get(homepageUrl, {
    headers: customHeaders,
    tags: { name: 'homepage' },
  });
  
  check(res, {
    'homepage loaded': (r) => r.status === 200,
  });
}

/**
 * Simulates a logout to clean up the session
 */
export function logout(customHeaders: CustomHeaders): void {
  const logoutUrl = __ENV.LOGOUT_URL || 'https://api.example.com/logout';
  
  const res = http.post(logoutUrl, null, {
    headers: customHeaders,
    tags: { name: 'logout' },
  });
  
  check(res, {
    'logout successful': (r) => r.status === 200 || r.status === 204,
  });
}
