/**
 * Utility functions for generating test scripts based on API responses
 */

/**
 * Generates test scripts based on the API response
 */
export function generateTestScript(response: any): string {
  if (!response) return ""

  const { status, headers, data } = response
  let tests: string[] = []

  // Add status code test
  tests.push(`pm.test("Status code is ${status}", function () {
  pm.response.to.have.status(${status});
});`)

  // Add response time test
  tests.push(`pm.test("Response time is acceptable", function () {
  pm.expect(pm.response.responseTime).to.be.below(1000);
});`)

  // Add content type test if available
  if (headers && headers["content-type"]) {
    const contentType = headers["content-type"]
    tests.push(`pm.test("Content-Type is ${contentType}", function () {
  pm.response.to.have.header("Content-Type");
  pm.expect(pm.response.headers.get("Content-Type")).to.include("${contentType}");
});`)
  }

  // Add data structure tests based on response data
  if (data) {
    try {
      // Generate tests based on the structure of the data
      const dataTests = generateDataTests(data)
      tests = [...tests, ...dataTests]
    } catch (error) {
      console.error("Error generating data tests:", error)
    }
  }

  return tests.join("\n\n")
}

/**
 * Generates tests based on the structure of the response data
 */
function generateDataTests(data: any): string[] {
  const tests: string[] = []

  // Check if data is an object
  if (typeof data === "object" && data !== null) {
    if (Array.isArray(data)) {
      // It's an array
      tests.push(`pm.test("Response is an array", function () {
  const json = pm.response.json();
  pm.expect(json).to.be.an("array");
${data.length > 0 ? `  pm.expect(json.length).to.be.above(0);` : ""}
});`)

      // If array has items, test the first item's structure
      if (data.length > 0) {
        const firstItem = data[0]
        if (typeof firstItem === "object" && firstItem !== null) {
          const itemKeys = Object.keys(firstItem)
          if (itemKeys.length > 0) {
            tests.push(`pm.test("Array items have the correct structure", function () {
  const json = pm.response.json();
  const firstItem = json[0];
  ${itemKeys.map((key) => `pm.expect(firstItem).to.have.property("${key}");`).join("\n  ")}
});`)
          }
        }
      }
    } else {
      // It's an object
      const keys = Object.keys(data)

      tests.push(`pm.test("Response has the correct structure", function () {
  const json = pm.response.json();
  ${keys.map((key) => `pm.expect(json).to.have.property("${key}");`).join("\n  ")}
});`)

      // Add specific tests for common response patterns
      if (data.success !== undefined) {
        tests.push(`pm.test("Success flag is ${data.success}", function () {
  const json = pm.response.json();
  pm.expect(json.success).to.be.${data.success ? "true" : "false"};
});`)
      }

      if (data.message) {
        tests.push(`pm.test("Response contains message", function () {
  const json = pm.response.json();
  pm.expect(json.message).to.be.a("string");
});`)
      }

      // If there's a data property that's an object, test its structure
      if (data.data && typeof data.data === "object" && !Array.isArray(data.data)) {
        const dataKeys = Object.keys(data.data)
        if (dataKeys.length > 0) {
          tests.push(`pm.test("Data object has the correct structure", function () {
  const json = pm.response.json();
  ${dataKeys.map((key) => `pm.expect(json.data).to.have.property("${key}");`).join("\n  ")}
});`)
        }
      }

      // If there's a data property that's an array, test it
      if (data.data && Array.isArray(data.data)) {
        tests.push(`pm.test("Data is an array", function () {
  const json = pm.response.json();
  pm.expect(json.data).to.be.an("array");
${data.data.length > 0 ? `  pm.expect(json.data.length).to.be.above(0);` : ""}
});`)
      }
    }
  }

  // If no specific data tests were generated, add a generic one
  if (tests.length === 0) {
    tests.push(`pm.test("Response body is not empty", function () {
  pm.expect(pm.response.text()).to.not.be.empty;
});`)
  }

  return tests
}

/**
 * Updates the request object with the test script
 */
export function updateRequestWithTests(request: any, testScript: string): any {
  if (!request) return request

  // Create a deep copy to avoid mutating the original
  const updatedRequest = JSON.parse(JSON.stringify(request))

  // Preserve important properties
  const originalId = updatedRequest.id
  const originalCollectionId = updatedRequest.collectionId

  // Convert the test script to the format expected by Postman
  const scriptExec = testScript.split("\n")

  // Create the test event
  const testEvent = {
    listen: "test",
    script: {
      type: "text/javascript",
      exec: scriptExec,
    },
  }

  // Fix: Place the event at the correct level
  // First, check if the request already has events at the request level
  if (updatedRequest.event) {
    // Find the test event if it exists
    const testEventIndex = updatedRequest.event.findIndex((e: any) => e.listen === "test")

    if (testEventIndex >= 0) {
      // Update existing test event
      updatedRequest.event[testEventIndex].script.exec = scriptExec
    } else {
      // Add new test event
      updatedRequest.event.push(testEvent)
    }
  } else {
    // Create a new event array with the test event
    updatedRequest.event = [testEvent]
  }

  // Ensure important properties are preserved
  if (originalId) {
    updatedRequest.id = originalId
  }

  if (originalCollectionId) {
    updatedRequest.collectionId = originalCollectionId
  }

  return updatedRequest
}
