/**
 * Utility functions for converting Postman collections to K6 scripts
 */

import { camelCase } from "../lib/utils";

// Type definitions
interface PostmanRequest {
  method: string;
  url: string | { raw: string; host?: string[]; path?: string[]; protocol?: string };
  header?: Array<{ key: string; value: string; disabled?: boolean }>;
  body?: {
    mode?: string;
    raw?: string;
    formdata?: Array<{ key: string; value: string; type: string }>;
    urlencoded?: Array<{ key: string; value: string; type: string }>;
  };
  auth?: {
    type: string;
    basic?: Array<{ key: string; value: string }>;
    bearer?: Array<{ key: string; value: string }>;
  };
}

interface PostmanItem {
  name: string;
  request?: PostmanRequest;
  item?: PostmanItem[];
  id?: string; // Some Postman collections include IDs for items
}

interface PostmanCollection {
  info: {
    name: string;
    description?: string;
    schema?: string;
  };
  item: PostmanItem[];
}

interface OldPostmanCollection {
  name: string;
  description?: string;
  requests?: any[];
  folders?: any[];
}

export type K6FileType = 'actions' | 'scenario' | 'config' | 'types' | 'combined';

export interface K6ScriptOutput {
  filename: string;
  content: string;
  folderPath?: string;
  fileType?: K6FileType;
}

// Helper functions
/**
 * Escapes special characters in strings for JavaScript
 */
const escapeString = (str: string): string => {
  return str
    .replace(/\\/g, '\\\\')
    .replace(/'/g, "\\'")
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
};

/**
 * Sanitizes a string to be used as a filename
 */
const sanitizeFilename = (name: string): string => {
  return name
    .replace(/[/\\?%*:|"<>]/g, '-') // Replace invalid filename characters
    .replace(/\s+/g, '_')           // Replace spaces with underscores
    .replace(/-+/g, '-')            // Replace multiple hyphens with a single one
    .toLowerCase();
};

/**
 * Generates a function name from a request name and method
 */
function generateFunctionName(name: string, method: string): string {
  const sanitizedName = name
    .toLowerCase()
    .replace(/[^a-z0-9\s]+/g, "")
    .trim()
    .replace(/\s+/g, "-")

  return camelCase(`${method.toLowerCase()}-${sanitizedName}`)
}

/**
 * Extracts the path from a URL
 */
function extractUrlPath(url: string): string {
  try {
    // Handle {{base_url}} variables
    if (url.includes("{{")) {
      const parts = url.split("/")
      const pathParts = parts.slice(3) // Skip the {{base_url}} part
      return "/" + pathParts.join("/")
    }

    // Handle full URLs
    if (url.startsWith("http")) {
      const urlObj = new URL(url)
      return urlObj.pathname
    }

    // Already a path
    return url
  } catch (e) {
    // Fallback: return as is
    return url
  }
}

/**
 * Recursively finds a folder by name in the collection
 */
const findFolderByName = (items: PostmanItem[], folderName: string): PostmanItem | null => {
  for (const item of items) {
    if (item.name === folderName && item.item) {
      return item;
    }
    if (item.item) {
      const found = findFolderByName(item.item, folderName);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

/**
 * Recursively finds a folder by path in the collection
 * Path format: "Folder / Subfolder / Nested folder"
 */
const findFolderByPath = (items: PostmanItem[], folderPath: string): PostmanItem | null => {
  if (!folderPath) return null;

  const pathParts = folderPath.split(' / ').filter(Boolean);
  if (pathParts.length === 0) return null;

  let currentItems = items;
  let currentFolder: PostmanItem | null = null;

  // Navigate through the path parts
  for (const part of pathParts) {
    currentFolder = null;

    // Find the folder at the current level
    for (const item of currentItems) {
      if (item.name === part && item.item) {
        currentFolder = item;
        currentItems = item.item;
        break;
      }
    }

    // If we couldn't find the folder at this level, return null
    if (!currentFolder) return null;
  }

  return currentFolder;
};

/**
 * Determines if the collection is in the newer v2.1 format
 */
export const isPostmanV2Collection = (collection: any): boolean => {
  return collection.info && collection.info.name && Array.isArray(collection.item);
};

/**
 * Converts an old format Postman collection to v2.1 format
 */
const convertOldFormatToV2 = (oldCollection: OldPostmanCollection): PostmanCollection => {
  const newCollection: PostmanCollection = {
    info: {
      name: oldCollection.name,
      description: oldCollection.description,
    },
    item: [],
  };

  // Convert folders
  if (oldCollection.folders && oldCollection.folders.length > 0) {
    newCollection.item = oldCollection.folders.map(folder => ({
      name: folder.name,
      item: [],
    }));
  }

  // Convert requests
  if (oldCollection.requests && oldCollection.requests.length > 0) {
    // Add requests that belong to folders
    oldCollection.requests.forEach(request => {
      if (request.folder) {
        const folderIndex = newCollection.item.findIndex(item => item.name === request.folder);
        if (folderIndex >= 0 && newCollection.item[folderIndex].item) {
          newCollection.item[folderIndex].item!.push({
            name: request.name,
            request: {
              method: request.method,
              url: request.url,
              header: request.headers,
              body: request.data,
            },
          });
        }
      } else {
        // Add requests that don't belong to any folder
        newCollection.item.push({
          name: request.name,
          request: {
            method: request.method,
            url: request.url,
            header: request.headers,
            body: request.data,
          },
        });
      }
    });
  }

  return newCollection;
};

/**
 * Generates code for a single request
 */
const generateRequestCode = (item: PostmanItem, indent = '  '): string => {
  if (!item.request) {
    return '';
  }

  const request = item.request;
  let code = `${indent}// Request: ${item.name}\n`;
  code += `${indent}{\n`;

  // URL
  let url = '';
  if (typeof request.url === 'string') {
    url = request.url;
  } else if (typeof request.url === 'object' && request.url.raw) {
    url = request.url.raw;
  }

  // Headers
  let headers = '{}';
  if (request.header && request.header.length > 0) {
    const headerObj = request.header
      .filter(h => !h.disabled)
      .reduce((acc, h) => {
        acc[h.key] = h.value;
        return acc;
      }, {} as Record<string, string>);
    headers = JSON.stringify(headerObj, null, 2).replace(/\n/g, `\n${indent}  `);
  }

  // Body
  let body = 'null';
  if (request.body) {
    if (request.body.mode === 'raw' && request.body.raw) {
      body = `\`${escapeString(request.body.raw)}\``;
    } else if (request.body.mode === 'formdata' && request.body.formdata) {
      // Handle form data
      const formData = request.body.formdata.reduce((acc, formItem) => {
        acc[formItem.key] = formItem.value;
        return acc;
      }, {} as Record<string, string>);
      body = JSON.stringify(formData, null, 2).replace(/\n/g, `\n${indent}  `);
    }
  }

  // Generate the request
  code += `${indent}  const response = http.${request.method.toLowerCase()}('${escapeString(url)}', ${body}, {\n`;
  code += `${indent}    headers: ${headers},\n`;
  code += `${indent}  });\n\n`;

  // Add checks
  code += `${indent}  check(response, {\n`;
  code += `${indent}    'status is 200': (r) => r.status === 200,\n`;
  code += `${indent}  });\n`;

  // Add sleep
  code += `${indent}  sleep(1);\n`;
  code += `${indent}}\n\n`;

  return code;
};

/**
 * Recursively generates code for all requests in the items
 */
const generateRequestsCode = (items: PostmanItem[], indent = '  '): string => {
  let code = '';

  items.forEach(item => {
    if (item.request) {
      // This is a request
      code += generateRequestCode(item, indent);
    } else if (item.item) {
      // This is a folder
      code += `${indent}group('${escapeString(item.name)}', function() {\n`;
      code += generateRequestsCode(item.item, `${indent}  `);
      code += `${indent}});\n\n`;
    }
  });

  return code;
};

/**
 * Generates the actions file with API call functions
 */
export function generateActions(items: any[], moduleName: string): string {
  let code = `import { group, check } from 'k6';
import { api } from '../lib/api';
import { CustomHeaders } from '../lib/types';

`

  // Group items by their parent folder
  const groupedItems: Record<string, any[]> = {}

  items.forEach((item) => {
    const path = item.path
    const parentFolder = path.length > 2 ? path[1] : "base"

    if (!groupedItems[parentFolder]) {
      groupedItems[parentFolder] = []
    }

    groupedItems[parentFolder].push(item)
  })

  // Generate functions for each endpoint
  Object.entries(groupedItems).forEach(([group, endpoints]) => {
    endpoints.forEach((endpoint) => {
      const functionName = generateFunctionName(endpoint.name, endpoint.method)
      const urlPath = extractUrlPath(endpoint.url)
      const methodLower = endpoint.method.toLowerCase()
      const hasBody = endpoint.body !== null

      let fnParams = "customHeaders: CustomHeaders"
      const successCheck = `"${endpoint.name} successful"`

      if (hasBody) {
        fnParams = `body: any, ${fnParams}`
      }

      if (endpoint.url.includes(":")) {
        // URL has path parameters
        fnParams = `params: Record<string, string>, ${fnParams}`
      }

      code += `export function ${functionName}(${fnParams}) {
  return group("${endpoint.name}", () => {
    const res = api.${methodLower}("${urlPath}"${hasBody ? ", body" : ""}${endpoint.url.includes(":") ? ", params" : ""}, "${endpoint.name.toLowerCase()}", customHeaders);
    check(res, { ${successCheck}: (r) => r.status >= 200 && r.status < 300 });
    return res;
  });
}

`
    })
  })

  return code
}

/**
 * Generates the scenario file with test flow implementation
 */
export function generateScenario(items: any[], moduleName: string): string {
  const actionFunctions = items.map((item) => generateFunctionName(item.name, item.method))

  let code = `import { sleep } from 'k6';
import { login, logout, callHomepage } from '../lib/auth';
import { ${actionFunctions.join(", ")} } from './${moduleName}-actions';

export function ${moduleName}Exec() {
  // Login and get session token
  const token = login();
  const deviceId = 'test-device-' + Math.random().toString(36).substring(2, 10);
  const customHeaders = { token, deviceId };

  // Navigate to homepage
  callHomepage(customHeaders);

  // Primary flow
`

  // Add function calls for each action
  items.forEach((item, index) => {
    const functionName = generateFunctionName(item.name, item.method)
    const hasBody = item.body !== null
    const hasParams = item.url.includes(":")

    const params = []

    if (hasBody) {
      // Parse the body if it's JSON
      let formattedBody = "{}"
      try {
        const bodyObj = JSON.parse(item.body)
        // Format the body with proper indentation
        formattedBody = JSON.stringify(bodyObj, null, 2)
      } catch (e) {
        // If parsing fails, use the raw body
        formattedBody = item.body
      }

      params.push(`${formattedBody}`)
    }

    if (hasParams) {
      params.push(`{
    // TODO: Add path parameters here or load from test data
  }`)
    }

    params.push("customHeaders")

    code += `  ${functionName}(${params.join(", ")});\n`

    // Add small delay between calls
    if (index < items.length - 1) {
      code += `  sleep(1);\n`
    }
  })

  code += `
  // Logout to clean up session
  logout(customHeaders);
}
`

  return code
}

/**
 * Generates the load configuration file
 */
export function generateLoadConfig(moduleName: string): string {
  return `import { Options } from 'k6/options';
import { ${moduleName}Exec } from './${moduleName}-scenario';

export const testTypes = {
  smoke: {
    vus: 1,
    duration: '1m',
    thresholds: {
      http_req_duration: ['p(95)<500'],
    },
  },
  load: {
    stages: [
      { duration: '5m', target: 10 },
      { duration: '10m', target: 10 },
      { duration: '5m', target: 0 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<500'],
      http_req_failed: ['rate<0.01'],
    },
  },
  stress: {
    stages: [
      { duration: '2m', target: 10 },
      { duration: '5m', target: 20 },
      { duration: '5m', target: 30 },
      { duration: '2m', target: 0 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<500'],
      http_req_failed: ['rate<0.05'],
    },
  },
  spike: {
    stages: [
      { duration: '2m', target: 5 },
      { duration: '1m', target: 25 },
      { duration: '2m', target: 5 },
    ],
    thresholds: {
      http_req_duration: ['p(99)<1500'],
      http_req_failed: ['rate<0.05'],
    },
  },
  endurance: {
    stages: [
      { duration: '5m', target: 5 },
      { duration: '30m', target: 5 },
      { duration: '5m', target: 0 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<500'],
      http_req_failed: ['rate<0.01'],
    },
  },
};

export function setup() {
  // Setup code if needed
  return {};
}

export function teardown(data: any) {
  // Teardown code if needed
}

export const options: Options = testTypes.smoke;

// Default exported function will be executed
export default function() {
  ${moduleName}Exec();
}
`
}

/**
 * Generates the types file with TypeScript interfaces
 */
export function generateTypes(): string {
  return `export interface CustomHeaders {
  token: string;
  deviceId: string;
  [key: string]: string;
}
`
}

/**
 * Converts PostmanItems to the format expected by the generator functions
 */
function convertItemsForGenerator(items: PostmanItem[], folderPath: string): any[] {
  const result: any[] = [];

  const processItems = (itemList: PostmanItem[], path: string[] = []) => {
    itemList.forEach(item => {
      if (item.request) {
        // This is a request
        const request = item.request;

        // URL
        let url = '';
        if (typeof request.url === 'string') {
          url = request.url;
        } else if (typeof request.url === 'object' && request.url.raw) {
          url = request.url.raw;
        }

        // Body
        let body = null;
        if (request.body) {
          if (request.body.mode === 'raw' && request.body.raw) {
            body = request.body.raw;
          } else if (request.body.mode === 'formdata' && request.body.formdata) {
            // Convert form data to JSON
            const formData: Record<string, string> = {};
            request.body.formdata.forEach(item => {
              formData[item.key] = item.value;
            });
            body = JSON.stringify(formData);
          }
        }

        // Add to result
        result.push({
          name: item.name,
          method: request.method,
          url: url,
          body: body,
          path: [...path, item.name],
        });
      } else if (item.item) {
        // This is a folder
        processItems(item.item, [...path, item.name]);
      }
    });
  };

  processItems(items, folderPath.split(' / '));

  return result;
}

/**
 * Generates a K6 script from Postman collection items (combined format)
 */
const generateK6Script = (
  collectionName: string,
  items: PostmanItem[],
  options: {
    vus: number;
    duration: string;
    thresholds: { [key: string]: string | string[] };
    includeChecks?: boolean;
    includeSleep?: boolean;
  }
): string => {
  const { vus, duration, thresholds } = options;

  // Generate imports and options
  let script = `// K6 script generated from Postman collection: ${collectionName}
import http from 'k6/http';
import { check, group, sleep } from 'k6';

// Test configuration
export const options = {
  vus: ${vus},
  duration: '${duration}',
  thresholds: {
${Object.entries(thresholds)
  .map(([metric, conditions]) => {
    const conditionsArray = Array.isArray(conditions) ? conditions : [conditions];
    return `    '${metric}': [${conditionsArray.map(c => `'${c}'`).join(', ')}]`;
  })
  .join(',\n')}
  },
};

// Main function
export default function() {
`;

  // Generate request functions
  script += generateRequestsCode(items);

  // Close the main function
  script += `  // End of test
  sleep(1);
}
`;

  return script;
};

/**
 * Extracts all folders from a Postman collection
 */
export const extractFolders = (collection: any): { id: string; name: string; path: string }[] => {
  if (!collection) return [];

  const folders: { id: string; name: string; path: string }[] = [];

  // Ensure we have a valid collection
  let items: PostmanItem[] = [];
  if (isPostmanV2Collection(collection)) {
    items = collection.item;
  } else if (collection.name && collection.folders) {
    // Handle older format
    items = collection.folders.map((folder: any) => ({
      name: folder.name,
      item: [],
    }));
  }

  // Extract folders recursively
  const extractFoldersRecursive = (folderItems: PostmanItem[], parentPath = '') => {
    folderItems.forEach((item, index) => {
      if (item.item) {
        const path = parentPath ? `${parentPath} / ${item.name}` : item.name;
        folders.push({
          id: `folder-${index}-${item.name.replace(/\s+/g, '-').toLowerCase()}`,
          name: item.name,
          path,
        });
        extractFoldersRecursive(item.item, path);
      }
    });
  };

  extractFoldersRecursive(items);
  return folders;
};

/**
 * Converts a Postman collection to K6 script(s)
 *
 * @param collection The Postman collection to convert
 * @param options Configuration options for the conversion
 * @returns Either a single script string or an array of script objects depending on outputFormat
 */
export const convertPostmanToK6 = (
  collection: any,
  options: {
    selectedFolders?: string[];
    selectedFolder?: string; // For backward compatibility
    vus?: number;
    duration?: string;
    thresholds?: { [key: string]: string };
    outputFormat?: 'single' | 'multiple';
    includeChecks?: boolean;
    includeSleep?: boolean;
    fileFormat?: 'combined' | 'split'; // New option for file format
  } = {}
): string | K6ScriptOutput[] => {
  // Default options
  const {
    vus = 1,
    duration = '1m',
    thresholds = { http_req_duration: ['p(95)<500'] },
    outputFormat = 'single',
    includeChecks = true,
    includeSleep = true,
    // Always use split file format
    fileFormat = 'split',
  } = options;

  // Initialize selectedFolders array
  const selectedFolders: string[] = options.selectedFolders || [];

  // For backward compatibility
  if (options.selectedFolder && !selectedFolders.includes(options.selectedFolder)) {
    selectedFolders.push(options.selectedFolder);
  }

  // Ensure we have a valid collection
  if (!collection) {
    throw new Error('Invalid collection: Collection is empty or undefined');
  }

  // Handle different Postman collection formats
  let postmanCollection: PostmanCollection;
  if (isPostmanV2Collection(collection)) {
    postmanCollection = collection as PostmanCollection;
  } else if (collection.name) {
    // Handle older format by converting to v2.1 format
    postmanCollection = convertOldFormatToV2(collection as OldPostmanCollection);
  } else {
    throw new Error('Invalid collection format: Not a recognized Postman collection');
  }

  // If using the combined file format (original behavior)
  if (fileFormat === 'combined') {
    // If no folders are selected, use the entire collection
    if (selectedFolders.length === 0) {
      if (outputFormat === 'single') {
        // Generate a single script with all items
        return generateK6Script(
          postmanCollection.info.name,
          postmanCollection.item,
          { vus, duration, thresholds, includeChecks, includeSleep }
        );
      }

      // Generate multiple scripts, one for each top-level folder
      const scripts: K6ScriptOutput[] = [];

      // Add a script for the entire collection
      scripts.push({
        filename: `${sanitizeFilename(postmanCollection.info.name)}_all.js`,
        content: generateK6Script(
          postmanCollection.info.name,
          postmanCollection.item,
          { vus, duration, thresholds, includeChecks, includeSleep }
        ),
        folderPath: 'All Requests',
        fileType: 'combined'
      });

      // Add scripts for each top-level folder
      postmanCollection.item.forEach(item => {
        if (item.item && item.item.length > 0) {
          scripts.push({
            filename: `${sanitizeFilename(postmanCollection.info.name)}_${sanitizeFilename(item.name)}.js`,
            content: generateK6Script(
              `${postmanCollection.info.name} - ${item.name}`,
              item.item,
              { vus, duration, thresholds, includeChecks, includeSleep }
            ),
            folderPath: item.name,
            fileType: 'combined'
          });
        }
      });

      return scripts;
    }

    // Process selected folders
    if (outputFormat === 'single') {
      // Collect all items from selected folders
      const selectedItems: PostmanItem[] = [];

      selectedFolders.forEach(folderPath => {
        const folder = findFolderByPath(postmanCollection.item, folderPath);
        if (folder && folder.item) {
          // Add a wrapper group for this folder
          const folderWrapper: PostmanItem = {
            name: folder.name,
            item: folder.item
          };
          selectedItems.push(folderWrapper);
        }
      });

      if (selectedItems.length === 0) {
        throw new Error('No valid folders selected');
      }

      // Generate a single script with all selected folders
      return generateK6Script(
        `${postmanCollection.info.name} - Selected Folders`,
        selectedItems,
        { vus, duration, thresholds, includeChecks, includeSleep }
      );
    }

    // Generate multiple scripts, one for each selected folder
    const scripts: K6ScriptOutput[] = [];

    selectedFolders.forEach(folderPath => {
      const folder = findFolderByPath(postmanCollection.item, folderPath);
      if (folder && folder.item) {
        scripts.push({
          filename: `${sanitizeFilename(postmanCollection.info.name)}_${sanitizeFilename(folder.name)}.js`,
          content: generateK6Script(
            `${postmanCollection.info.name} - ${folder.name}`,
            folder.item,
            { vus, duration, thresholds, includeChecks, includeSleep }
          ),
          folderPath: folderPath,
          fileType: 'combined'
        });
      }
    });

    if (scripts.length === 0) {
      throw new Error('No valid folders selected');
    }

    return scripts;
  }

  // Split file format - generate separate files for actions, scenarios, config, and types
  const scripts: K6ScriptOutput[] = [];

  // Function to generate all file types for a folder
  const generateFilesForFolder = (
    folderName: string,
    folderItems: PostmanItem[],
    folderPath: string
  ) => {
    const baseName = sanitizeFilename(postmanCollection.info.name);
    const folderBaseName = sanitizeFilename(folderName);

    // Convert PostmanItems to the format expected by the new generator functions
    const convertedItems = convertItemsForGenerator(folderItems, folderPath);

    // Generate actions file
    scripts.push({
      filename: `${baseName}_${folderBaseName}-actions.ts`,
      content: generateActions(
        convertedItems,
        folderBaseName
      ),
      folderPath: folderPath,
      fileType: 'actions'
    });

    // Generate scenario file
    scripts.push({
      filename: `${baseName}_${folderBaseName}-scenario.ts`,
      content: generateScenario(
        convertedItems,
        folderBaseName
      ),
      folderPath: folderPath,
      fileType: 'scenario'
    });

    // Generate config file
    scripts.push({
      filename: `${baseName}_${folderBaseName}-config.ts`,
      content: generateLoadConfig(
        folderBaseName
      ),
      folderPath: folderPath,
      fileType: 'config'
    });

    // Generate types file
    scripts.push({
      filename: `${baseName}_${folderBaseName}-types.ts`,
      content: generateTypes(),
      folderPath: folderPath,
      fileType: 'types'
    });
  };

  // If no folders are selected, use the entire collection
  if (selectedFolders.length === 0) {
    if (outputFormat === 'single') {
      // Generate files for the entire collection
      generateFilesForFolder(
        postmanCollection.info.name,
        postmanCollection.item,
        'All Requests'
      );
    } else {
      // Generate files for each top-level folder
      postmanCollection.item.forEach(item => {
        if (item.item && item.item.length > 0) {
          generateFilesForFolder(
            item.name,
            item.item,
            item.name
          );
        }
      });
    }
  } else {
    // Process selected folders
    selectedFolders.forEach(folderPath => {
      const folder = findFolderByPath(postmanCollection.item, folderPath);
      if (folder && folder.item) {
        generateFilesForFolder(
          folder.name,
          folder.item,
          folderPath
        );
      }
    });
  }

  if (scripts.length === 0) {
    throw new Error('No valid folders selected');
  }

  return scripts;
};

