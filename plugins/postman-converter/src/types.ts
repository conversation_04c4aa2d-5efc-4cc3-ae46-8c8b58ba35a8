export interface Collection {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  content?: string; // Only included in detailed view
  version?: number; // Only included in detailed view
}

export interface CollectionVersion {
  id: string;
  collection_id: string;
  version: number;
  content: string;
  created_at: string;
  user_id: string;
}

export interface CollectionCreateRequest {
  name: string;
  description: string;
  content: string;
}

export interface CollectionUpdateRequest {
  name?: string;
  description?: string;
  content?: string;
}

// API Testing Types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

export interface ApiRequest {
  id: string;
  name: string;
  method: HttpMethod;
  url: string;
  headers: { key: string; value: string; enabled: boolean }[];
  params: { key: string; value: string; enabled: boolean }[];
  body: {
    mode: 'none' | 'raw' | 'form-data' | 'urlencoded';
    raw?: string;
    formData?: { key: string; value: string; type: string }[];
    urlencoded?: { key: string; value: string }[];
    enabled?: boolean; // Whether the body is enabled
  };
  auth?: {
    type: 'none' | 'basic' | 'bearer' | 'apiKey';
    basic?: { username: string; password: string };
    bearer?: { token: string };
    apiKey?: { key: string; value: string; in: 'header' | 'query' };
  };
  preRequestScript?: string; // Pre-request script content
  testScript?: string; // Test script content
  lastTestResults?: TestResult[]; // Results from the last test run
}

export interface ApiResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  time: number; // in milliseconds
  size: number;
  error?: string;
  url?: string;
}

export interface ApiEnvironment {
  id: string;
  name: string;
  variables: { key: string; value: string; enabled: boolean }[];
}

export interface ApiFolder {
  id: string;
  name: string;
  parentId?: string;
  requests: string[]; // Array of request IDs
  folders: ApiFolder[]; // Array of subfolders
}

export interface ApiCollection {
  id: string;
  name: string;
  description: string;
  folders: ApiFolder[];
  requests: Record<string, ApiRequest>;
  environments: ApiEnvironment[];
  _orphanedRequests?: string[]; // For debugging - requests that couldn't be added to folders
}
