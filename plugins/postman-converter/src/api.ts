import { createApiRef, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@backstage/core-plugin-api';
import { Collection, CollectionCreateRequest, CollectionUpdateRequest, CollectionVersion } from './types';

export interface PostmanConverterApi {
  getCollections(): Promise<Collection[]>;
  getCollectionById(id: string): Promise<Collection>;
  createCollection(data: CollectionCreateRequest): Promise<Collection>;
  updateCollection(id: string, data: CollectionUpdateRequest): Promise<Collection>;
  deleteCollection(id: string): Promise<void>;
  getCollectionVersions(id: string): Promise<CollectionVersion[]>;
  rollbackToVersion(id: string, versionNumber: number): Promise<CollectionVersion>;
}

export const postmanConverterApiRef = createApiRef<PostmanConverterApi>({
  id: 'plugin.postman-converter.api',
});

export class PostmanConverterClient implements PostmanConverterApi {
  private readonly discoveryApi: DiscoveryApi;
  private readonly fetchApi: FetchApi;

  constructor(options: { discoveryApi: <PERSON><PERSON>pi; fetchApi: Fetch<PERSON>pi }) {
    this.discoveryApi = options.discoveryApi;
    this.fetchApi = options.fetchApi;
  }

  private async getBaseUrl() {
    return `${await this.discoveryApi.getBaseUrl('postman-converter')}/collections`;
  }

  async getCollections(): Promise<Collection[]> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch collections: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async getCollectionById(id: string): Promise<Collection> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch collection: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async createCollection(data: CollectionCreateRequest): Promise<Collection> {
    const url = await this.getBaseUrl();
    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create collection: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async updateCollection(id: string, data: CollectionUpdateRequest): Promise<Collection> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update collection: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async deleteCollection(id: string): Promise<void> {
    const url = `${await this.getBaseUrl()}/${id}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error(`Failed to delete collection: ${response.statusText}`);
    }
  }

  async getCollectionVersions(id: string): Promise<CollectionVersion[]> {
    const url = `${await this.getBaseUrl()}/${id}/history`;
    const response = await this.fetchApi.fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch collection versions: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async rollbackToVersion(id: string, versionNumber: number): Promise<CollectionVersion> {
    const url = `${await this.getBaseUrl()}/${id}/rollback/${versionNumber}`;
    const response = await this.fetchApi.fetch(url, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error(`Failed to rollback collection: ${response.statusText}`);
    }
    
    return await response.json();
  }
}
