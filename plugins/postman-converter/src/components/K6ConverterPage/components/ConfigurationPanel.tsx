import React from 'react';
import {
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  TextField,
  FormControlLabel,
  Checkbox,
  makeStyles,
} from '@material-ui/core';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

const useStyles = makeStyles(theme => ({
  accordion: {
    marginBottom: theme.spacing(2),
  },
}));

interface ConfigurationPanelProps {
  expanded: boolean;
  onExpandChange: () => void;
  vus: number;
  duration: string;
  includeChecks: boolean;
  includeSleep: boolean;
  onConfigChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCheckboxChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
}

/**
 * Component for configuring K6 test options
 */
export const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  expanded,
  onExpandChange,
  vus,
  duration,
  includeChecks,
  includeSleep,
  onConfigChange,
  onCheckboxChange,
}) => {
  const classes = useStyles();

  return (
    <Accordion
      className={classes.accordion}
      expanded={expanded}
      onChange={onExpandChange}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography>3. Configure Test Options</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <TextField
              label="Virtual Users"
              type="number"
              value={vus}
              onChange={onConfigChange('vus')}
              fullWidth
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              label="Duration"
              value={duration}
              onChange={onConfigChange('duration')}
              fullWidth
              helperText="e.g., 30s, 1m, 1h"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={includeChecks}
                  onChange={onCheckboxChange('includeChecks')}
                  color="primary"
                />
              }
              label="Include status checks"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={includeSleep}
                  onChange={onCheckboxChange('includeSleep')}
                  color="primary"
                />
              }
              label="Include sleep between requests"
            />
          </Grid>
        </Grid>
      </AccordionDetails>
    </Accordion>
  );
};
