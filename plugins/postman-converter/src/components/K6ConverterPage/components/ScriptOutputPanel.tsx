import React from 'react';
import {
  Typography,
  IconButton,
  Tooltip,
  Box,
  makeStyles,
} from '@material-ui/core';
import { CodeSnippet } from '@backstage/core-components';
import FileCopyIcon from '@material-ui/icons/FileCopy';
import { K6ScriptOutput } from '../../../utils/k6Converter';
import { ScriptTabs } from './ScriptTabs';

const useStyles = makeStyles(theme => ({
  codeContainer: {
    marginTop: theme.spacing(2),
    position: 'relative',
  },
  copyButton: {
    position: 'absolute',
    top: theme.spacing(1),
    right: theme.spacing(1),
    zIndex: 1,
  },
}));

interface ScriptOutputPanelProps {
  scripts: K6ScriptOutput[];
  activeScriptIndex: number;
  onTabChange: (newIndex: number) => void;
  onCopyToClipboard: (content: string) => void;
}

/**
 * Component for displaying generated K6 scripts
 */
export const ScriptOutputPanel: React.FC<ScriptOutputPanelProps> = ({
  scripts,
  activeScriptIndex,
  onTabChange,
  onCopyToClipboard,
}) => {
  const classes = useStyles();

  if (scripts.length === 0) {
    return (
      <Typography variant="body2" color="textSecondary">
        Select a collection and generate a script to see the result here
      </Typography>
    );
  }

  const activeScript = scripts[activeScriptIndex];

  return (
    <>
      <ScriptTabs
        scripts={scripts}
        activeIndex={activeScriptIndex}
        onTabChange={onTabChange}
      />

      <div className={classes.codeContainer}>
        <Tooltip title="Copy to clipboard">
          <IconButton
            className={classes.copyButton}
            onClick={() => onCopyToClipboard(activeScript.content)}
            size="small"
            color="primary"
          >
            <FileCopyIcon />
          </IconButton>
        </Tooltip>
        <Typography variant="subtitle2" gutterBottom>
          {activeScript.filename}
        </Typography>
        <CodeSnippet
          text={activeScript.content}
          language={activeScript.filename.endsWith('.ts') ? 'typescript' : 'javascript'}
          showLineNumbers
        />
      </div>
    </>
  );
};
