import React from 'react';
import { Typography, Tabs, Tab, makeStyles } from '@material-ui/core';
import { K6ScriptOutput } from '../../../utils/k6Converter';

const useStyles = makeStyles(theme => ({
  scriptTabs: {
    marginBottom: theme.spacing(2),
  },
  scriptTab: {
    minWidth: 120,
  },
}));

interface ScriptTabsProps {
  scripts: K6ScriptOutput[];
  activeIndex: number;
  onTabChange: (newIndex: number) => void;
}

/**
 * Component for displaying script tabs
 */
export const ScriptTabs: React.FC<ScriptTabsProps> = ({
  scripts,
  activeIndex,
  onTabChange,
}) => {
  const classes = useStyles();

  if (scripts.length <= 1) {
    return null;
  }

  // Group scripts by folder path
  const scriptsByFolder: Record<string, K6ScriptOutput[]> = {};
  scripts.forEach(script => {
    const folderPath = script.folderPath || 'Default';
    if (!scriptsByFolder[folderPath]) {
      scriptsByFolder[folderPath] = [];
    }
    scriptsByFolder[folderPath].push(script);
  });

  const folderPaths = Object.keys(scriptsByFolder);

  // If we have multiple folders, show folder tabs first
  if (folderPaths.length > 1) {
    const currentFolderPath = scripts[activeIndex].folderPath || 'Default';
    const currentFolderScripts = scriptsByFolder[currentFolderPath];
    const currentFolderIndex = folderPaths.indexOf(currentFolderPath);

    return (
      <>
        <Typography variant="subtitle1" gutterBottom>
          Select Folder:
        </Typography>
        <Tabs
          value={currentFolderIndex}
          onChange={(_, newValue) => {
            // Find the first script in the selected folder
            const folderPath = folderPaths[newValue];
            const folderScripts = scriptsByFolder[folderPath];
            if (folderScripts && folderScripts.length > 0) {
              // Find the index of this script in the overall scripts array
              const newIndex = scripts.findIndex(s => s === folderScripts[0]);
              if (newIndex >= 0) {
                onTabChange(newIndex);
              }
            }
          }}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          className={classes.scriptTabs}
        >
          {folderPaths.map((folderPath, index) => (
            <Tab
              key={index}
              label={folderPath}
              className={classes.scriptTab}
            />
          ))}
        </Tabs>

        {/* Show file type tabs for the selected folder if it has multiple scripts */}
        {currentFolderScripts.length > 1 && (
          <>
            <Typography variant="subtitle1" gutterBottom style={{ marginTop: '16px' }}>
              Select File Type:
            </Typography>
            <Tabs
              value={currentFolderScripts.findIndex(s => s === scripts[activeIndex])}
              onChange={(_, newValue) => {
                const script = currentFolderScripts[newValue];
                const newIndex = scripts.findIndex(s => s === script);
                if (newIndex >= 0) {
                  onTabChange(newIndex);
                }
              }}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
              className={classes.scriptTabs}
            >
              {currentFolderScripts.map((script, index) => (
                <Tab
                  key={index}
                  label={script.fileType || 'Script'}
                  className={classes.scriptTab}
                />
              ))}
            </Tabs>
          </>
        )}
      </>
    );
  }

  // If we have only one folder but multiple scripts (file types)
  return (
    <Tabs
      value={activeIndex}
      onChange={(_, newValue) => onTabChange(newValue)}
      indicatorColor="primary"
      textColor="primary"
      variant="scrollable"
      scrollButtons="auto"
      className={classes.scriptTabs}
    >
      {scripts.map((script, index) => (
        <Tab
          key={index}
          label={script.fileType || script.filename}
          className={classes.scriptTab}
        />
      ))}
    </Tabs>
  );
};
