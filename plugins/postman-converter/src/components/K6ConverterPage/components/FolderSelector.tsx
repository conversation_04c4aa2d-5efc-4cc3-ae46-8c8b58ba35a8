import React from 'react';
import {
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Box,
  makeStyles,
} from '@material-ui/core';
import FolderIcon from '@material-ui/icons/Folder';

const useStyles = makeStyles(theme => ({
  formControl: {
    width: '100%',
    marginBottom: theme.spacing(2),
  },
  treeContainer: {
    maxHeight: '400px',
    overflow: 'auto',
    padding: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  folderItem: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  folderItemSelected: {
    backgroundColor: theme.palette.action.selected,
    '&:hover': {
      backgroundColor: theme.palette.action.selected,
    },
  },
  selectedFolder: {
    backgroundColor: theme.palette.action.selected,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
}));

interface FolderSelectorProps {
  folders: Array<{ id: string; name: string; path: string }>;
  selectedFolders: string[];
  selectionMode: 'single' | 'multiple';
  onFolderSelect: (folderId: string) => void;
  onSelectionModeChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

/**
 * Component for selecting folders from a collection
 */
export const FolderSelector: React.FC<FolderSelectorProps> = ({
  folders,
  selectedFolders,
  selectionMode,
  onFolderSelect,
  onSelectionModeChange,
}) => {
  const classes = useStyles();

  return (
    <>
      <Typography variant="h6" gutterBottom>
        2. Select Folders
      </Typography>

      <FormControl className={classes.formControl}>
        <InputLabel id="selection-mode-label">Selection Mode</InputLabel>
        <Select
          labelId="selection-mode-label"
          id="selection-mode"
          value={selectionMode}
          onChange={onSelectionModeChange}
        >
          <MenuItem value="single">Single Folder</MenuItem>
          <MenuItem value="multiple">Multiple Folders</MenuItem>
        </Select>
      </FormControl>

      {/* File Format is always set to "split" */}
      <FormControl className={classes.formControl}>
        <InputLabel id="file-format-label">File Format</InputLabel>
        <Select
          labelId="file-format-label"
          id="file-format"
          value="split"
          disabled
        >
          <MenuItem value="split">Split Files (Actions, Scenario, Config, Types)</MenuItem>
        </Select>
      </FormControl>

      {folders.length > 0 ? (
        <Paper variant="outlined" className={classes.treeContainer}>
          <List dense component="div">
            {folders.map(folder => (
              <ListItem
                button
                key={folder.id}
                onClick={() => onFolderSelect(folder.id)}
                className={
                  selectedFolders.includes(folder.path)
                    ? classes.folderItemSelected
                    : classes.folderItem
                }
              >
                <ListItemIcon>
                  <FolderIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary={folder.name} />
              </ListItem>
            ))}
          </List>
        </Paper>
      ) : (
        <Typography variant="body2" color="textSecondary">
          No folders found in this collection
        </Typography>
      )}

      {selectedFolders.length > 0 && (
        <Box className={classes.selectedFolder}>
          <Typography variant="body2">
            <strong>Selected {selectedFolders.length > 1 ? 'Folders' : 'Folder'}:</strong>
            <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>
              {selectedFolders.map((folder, index) => (
                <li key={index}>{folder}</li>
              ))}
            </ul>
          </Typography>
        </Box>
      )}
    </>
  );
};
