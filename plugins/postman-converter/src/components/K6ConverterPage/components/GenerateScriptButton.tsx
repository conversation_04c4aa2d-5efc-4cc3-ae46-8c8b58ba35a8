import React from 'react';
import { Button, CircularProgress } from '@material-ui/core';
import CodeIcon from '@material-ui/icons/Code';

interface GenerateScriptButtonProps {
  isGenerating: boolean;
  onClick: () => void;
}

/**
 * Button component for generating K6 scripts
 */
export const GenerateScriptButton: React.FC<GenerateScriptButtonProps> = ({
  isGenerating,
  onClick,
}) => {
  return (
    <Button
      variant="contained"
      color="primary"
      fullWidth
      onClick={onClick}
      disabled={isGenerating}
      startIcon={isGenerating ? <CircularProgress size={20} /> : <CodeIcon />}
    >
      {isGenerating ? 'Generating...' : 'Generate K6 Script'}
    </Button>
  );
};
