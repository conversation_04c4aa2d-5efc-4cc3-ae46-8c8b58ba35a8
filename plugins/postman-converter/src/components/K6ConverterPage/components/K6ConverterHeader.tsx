import React from 'react';
import { Box, Typography, Button, makeStyles } from '@material-ui/core';
import { InfoCard } from '@backstage/core-components';
import K6Icon from '@material-ui/icons/Speed';

const useStyles = makeStyles(theme => ({
  infoCard: {
    marginBottom: theme.spacing(3),
  },
}));

/**
 * Header component for the K6 Converter page
 */
export const K6ConverterHeader: React.FC = () => {
  const classes = useStyles();

  return (
    <InfoCard
      title={
        <Box display="flex" alignItems="center">
          <K6Icon style={{ marginRight: '8px' }} />
          K6 Converter
        </Box>
      }
      className={classes.infoCard}
    >
      <Typography variant="body1" paragraph>
        Transform your Postman collections into powerful K6 load testing scripts.
        Easily configure, run, and analyze performance tests to ensure your APIs can handle the load.
      </Typography>
      <Button
        variant="outlined"
        color="primary"
        href="https://k6.io/docs/"
        target="_blank"
        rel="noopener"
        startIcon={<K6Icon />}
      >
        Learn more about K6
      </Button>
    </InfoCard>
  );
};
