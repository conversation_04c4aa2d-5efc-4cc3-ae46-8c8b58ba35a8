import { useState, useEffect } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { Collection } from '../../../types';
import { extractFolders, convertPostmanToK6, K6ScriptOutput } from '../../../utils/k6Converter';

interface K6ConverterConfig {
  vus: number;
  duration: string;
  includeChecks: boolean;
  includeSleep: boolean;
  outputFormat: 'single' | 'multiple';
  selectionMode: 'single' | 'multiple';
  fileFormat: 'split' | 'combined';
}

interface UseK6ConverterProps {
  selectedCollectionId: string;
  fetchCollectionById: (id: string) => Promise<Collection | null>;
}

export const useK6Converter = ({ selectedCollectionId, fetchCollectionById }: UseK6ConverterProps) => {
  const errorApi = useApi(errorApiRef);

  // Collection state
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  const [folders, setFolders] = useState<Array<{ id: string; name: string; path: string }>>([]);
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  // Script state
  const [k6Scripts, setK6Scripts] = useState<K6ScriptOutput[]>([]);
  const [activeScriptIndex, setActiveScriptIndex] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  // Configuration state
  const [configExpanded, setConfigExpanded] = useState<boolean>(false);
  const [config, setConfig] = useState<K6ConverterConfig>({
    vus: 1,
    duration: '1m',
    includeChecks: true,
    includeSleep: true,
    outputFormat: 'single',
    selectionMode: 'single',
    fileFormat: 'split', // Always set to 'split'
  });

  // Fetch selected collection details
  useEffect(() => {
    const fetchCollectionDetails = async () => {
      if (!selectedCollectionId) {
        setSelectedCollection(null);
        setFolders([]);
        return;
      }

      const collection = await fetchCollectionById(selectedCollectionId);
      setSelectedCollection(collection);

      // Parse collection content to extract folders
      if (collection?.content) {
        try {
          const parsedContent = JSON.parse(collection.content);
          const extractedFolders = extractFolders(parsedContent);
          setFolders(extractedFolders);
        } catch (e) {
          errorApi.post(new Error('Failed to parse collection content'));
          setFolders([]);
        }
      }
    };

    fetchCollectionDetails();
  }, [selectedCollectionId, fetchCollectionById, errorApi]);

  // Reset selections when collection changes
  useEffect(() => {
    setSelectedFolders([]);
    setK6Scripts([]);
    setActiveScriptIndex(0);
  }, [selectedCollectionId]);

  // Handle folder selection
  const handleFolderSelect = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    if (!folder) return;

    if (config.selectionMode === 'single') {
      // Single selection mode
      setSelectedFolders([folder.path]);
    } else {
      // Multiple selection mode
      const folderIndex = selectedFolders.indexOf(folder.path);
      if (folderIndex >= 0) {
        // Remove folder if already selected
        setSelectedFolders(selectedFolders.filter(f => f !== folder.path));
      } else {
        // Add folder to selection
        setSelectedFolders([...selectedFolders, folder.path]);
      }
    }
  };

  // Handle configuration changes
  const handleConfigChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setConfig({
      ...config,
      [field]: field === 'vus' ? Number(event.target.value) : event.target.value,
    });
  };

  const handleCheckboxChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setConfig({
      ...config,
      [field]: event.target.checked,
    });
  };

  const handleSelectModeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const newMode = event.target.value as 'single' | 'multiple';
    setConfig({
      ...config,
      selectionMode: newMode,
    });

    // Reset selected folders when changing mode
    setSelectedFolders([]);
  };

  const handleOutputFormatChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setConfig({
      ...config,
      outputFormat: event.target.value as 'single' | 'multiple',
    });
  };

  // Generate K6 script
  const generateK6Script = async () => {
    if (!selectedCollection || !selectedCollection.content) {
      errorApi.post(new Error('No collection selected or collection has no content'));
      return;
    }

    setIsGenerating(true);

    try {
      const parsedContent = JSON.parse(selectedCollection.content);

      const result = convertPostmanToK6(parsedContent, {
        selectedFolders: selectedFolders,
        vus: config.vus,
        duration: config.duration,
        includeChecks: config.includeChecks,
        includeSleep: config.includeSleep,
        outputFormat: config.outputFormat,
        fileFormat: config.fileFormat,
      });

      if (typeof result === 'string') {
        // Single script result
        setK6Scripts([{
          filename: `${selectedCollection.name}.js`,
          content: result,
          folderPath: selectedFolders.length === 1 ? selectedFolders[0] : 'All',
          fileType: 'combined'
        }]);
      } else {
        // Multiple script results
        setK6Scripts(result);
      }

      setActiveScriptIndex(0);
    } catch (e) {
      errorApi.post(e instanceof Error ? e : new Error('Failed to generate K6 script'));
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    selectedCollection,
    folders,
    selectedFolders,
    k6Scripts,
    activeScriptIndex,
    isGenerating,
    configExpanded,
    config,
    setActiveScriptIndex,
    handleFolderSelect,
    handleConfigChange,
    handleCheckboxChange,
    handleSelectModeChange,
    setConfigExpanded,
    generateK6Script,
  };
};
