import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { postmanConverterApiRef } from '../../../api';
import { Collection } from '../../../types';

/**
 * Hook for fetching and managing collections
 */
export const useCollections = () => {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  // Fetch collections
  const { value: collections, loading, error } = useAsync(async () => {
    try {
      return await postmanConverterApi.getCollections();
    } catch (e) {
      errorApi.post(new Error(`Failed to fetch collections: ${e instanceof Error ? e.message : String(e)}`));
      return [];
    }
  }, []);

  /**
   * Fetch a collection by ID
   */
  const fetchCollectionById = async (id: string): Promise<Collection | null> => {
    if (!id) return null;
    
    try {
      return await postmanConverterApi.getCollectionById(id);
    } catch (e) {
      errorApi.post(new Error(`Failed to fetch collection details: ${e instanceof Error ? e.message : String(e)}`));
      return null;
    }
  };

  return {
    collections: collections || [],
    loading,
    error,
    fetchCollectionById,
  };
};
