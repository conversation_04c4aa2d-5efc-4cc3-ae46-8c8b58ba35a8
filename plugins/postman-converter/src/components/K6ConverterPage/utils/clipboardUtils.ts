/**
 * Copy content to clipboard
 * 
 * @param content - The content to copy
 * @returns A promise that resolves with success or error message
 */
export const copyToClipboard = async (content: string): Promise<{ success: boolean; message: string }> => {
  try {
    await navigator.clipboard.writeText(content);
    return { success: true, message: '<PERSON>ript copied to clipboard!' };
  } catch (error) {
    return { success: false, message: 'Failed to copy script' };
  }
};
