import React, { useState } from 'react';
import {
  Grid,
  Box,
  Paper,
  Snackbar,
  makeStyles,
  Button,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import {
  EmptyState,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';

// Import custom hooks
import { useCollections, useK6Converter } from './hooks';

// Import components
import {
  K6ConverterHeader,
  CollectionSelector,
  FolderSelector,
  ConfigurationPanel,
  ScriptOutputPanel,
  GenerateScriptButton,
} from './components';

// Import utilities
import { copyToClipboard } from './utils';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  contentContainer: {
    padding: theme.spacing(3),
  },
}));

/**
 * K6 Converter Page Component
 * Allows users to convert Postman collections to K6 load testing scripts
 */
export const K6ConverterPage: React.FC = () => {
  const classes = useStyles();

  // Collection state
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');

  // Notification state
  const [showSnackbar, setShowSnackbar] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Custom hooks
  const { collections, loading, error, fetchCollectionById } = useCollections();
  const {
    selectedCollection,
    folders,
    selectedFolders,
    k6Scripts,
    activeScriptIndex,
    isGenerating,
    configExpanded,
    config,
    setActiveScriptIndex,
    handleFolderSelect,
    handleConfigChange,
    handleCheckboxChange,
    handleSelectModeChange,
    setConfigExpanded,
    generateK6Script,
  } = useK6Converter({ selectedCollectionId, fetchCollectionById });

  // Event handlers
  const handleCollectionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedCollectionId(event.target.value as string);
  };

  const handleCopyToClipboard = async (content: string) => {
    const result = await copyToClipboard(content);
    setSnackbarMessage(result.message);
    setShowSnackbar(true);
  };

  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };

  // Loading and error states
  if (loading) {
    return <Progress />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <K6ConverterHeader />
        </Grid>

        {collections && collections.length > 0 ? (
          <>
            {/* Left panel - Collection selection and configuration */}
            <Grid item xs={12} md={4}>
              <Paper>
                <Box p={3}>
                  <CollectionSelector
                    collections={collections}
                    selectedCollectionId={selectedCollectionId}
                    onCollectionChange={handleCollectionChange}
                  />

                  {selectedCollection && (
                    <>
                      <FolderSelector
                        folders={folders}
                        selectedFolders={selectedFolders}
                        selectionMode={config.selectionMode}
                        onFolderSelect={handleFolderSelect}
                        onSelectionModeChange={handleSelectModeChange}
                      />

                      <ConfigurationPanel
                        expanded={configExpanded}
                        onExpandChange={() => setConfigExpanded(!configExpanded)}
                        vus={config.vus}
                        duration={config.duration}
                        includeChecks={config.includeChecks}
                        includeSleep={config.includeSleep}
                        onConfigChange={handleConfigChange}
                        onCheckboxChange={handleCheckboxChange}
                      />

                      <GenerateScriptButton
                        isGenerating={isGenerating}
                        onClick={generateK6Script}
                      />
                    </>
                  )}
                </Box>
              </Paper>
            </Grid>

            {/* Right panel - Generated scripts */}
            <Grid item xs={12} md={8}>
              <Paper>
                <Box p={3}>
                  <Box mb={2}>
                    <h2>Generated K6 Scripts</h2>
                  </Box>
                  <ScriptOutputPanel
                    scripts={k6Scripts}
                    activeScriptIndex={activeScriptIndex}
                    onTabChange={setActiveScriptIndex}
                    onCopyToClipboard={handleCopyToClipboard}
                  />
                </Box>
              </Paper>
            </Grid>
          </>
        ) : (
          <Grid item xs={12}>
            <EmptyState
              missing="data"
              title="No Collections Available"
              description="You need to create Postman collections first before you can convert them to K6 scripts."
              action={
                <Button
                  variant="contained"
                  color="primary"
                  href="/postman-converter/new"
                >
                  Create Collection
                </Button>
              }
            />
          </Grid>
        )}
      </Grid>

      {/* Notifications */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity="success">
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};
