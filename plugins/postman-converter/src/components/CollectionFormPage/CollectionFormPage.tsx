import React, { useState } from 'react';
import {
  Content,
  ContentHeader,
  SupportButton,
  InfoCard,
} from '@backstage/core-components';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import {
  But<PERSON>,
  Grid,
  TextField,
  Typography,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useNavigate } from 'react-router-dom';
import { postmanConverterApiRef } from '../../api';

export const CollectionFormPage = () => {
  const navigate = useNavigate();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  const [formState, setFormState] = useState({
    name: '',
    description: '',
    content: '{}',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formState.name.trim()) {
      newErrors.name = 'Name is required';
    }

    try {
      const contentJson = JSON.parse(formState.content);

      // Validate Postman collection format
      const isValidPostmanCollection = (
        // Check for v2.1 format
        (contentJson.info && contentJson.info.name && Array.isArray(contentJson.item)) ||
        // Check for older format
        (contentJson.name && (Array.isArray(contentJson.requests) || Array.isArray(contentJson.folders)))
      );

      if (!isValidPostmanCollection) {
        newErrors.content = 'Invalid Postman collection format. Please export from Postman using Collection v2.1 format.';
      }
    } catch (e) {
      newErrors.content = 'Invalid JSON format. Please ensure you\'ve copied the entire collection JSON.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const collection = await postmanConverterApi.createCollection(formState);
      navigate(`/postman-converter/${collection.id}`);
    } catch (e) {
      errorApi.post(e);
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormState({
      ...formState,
      [field]: event.target.value,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        // Try to parse to validate JSON
        JSON.parse(content);

        // If the file name doesn't have an extension, use it as the collection name
        if (file.name && !formState.name && file.name.endsWith('.json')) {
          const nameWithoutExtension = file.name.replace(/\.json$/, '');
          setFormState({
            ...formState,
            name: nameWithoutExtension,
            content,
          });
        } else {
          setFormState({
            ...formState,
            content,
          });
        }
      } catch (error) {
        setErrors({
          ...errors,
          content: 'Invalid JSON file. Please ensure you\'ve exported a valid Postman collection.',
        });
      }
    };
    reader.readAsText(file);
  };

  const handleCancel = () => {
    navigate('/postman-converter');
  };

  return (
    <Content>
      <ContentHeader title="Add Postman Collection">
        <SupportButton>
          Upload a new Postman collection
        </SupportButton>
      </ContentHeader>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    label="Collection Name"
                    value={formState.name}
                    onChange={handleChange('name')}
                    error={!!errors.name}
                    helperText={errors.name}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    multiline
                    rows={4}
                    value={formState.description}
                    onChange={handleChange('description')}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12}>
                      <input
                        accept=".json"
                        style={{ display: 'none' }}
                        id="collection-file-upload"
                        type="file"
                        onChange={handleFileUpload}
                      />
                      <label htmlFor="collection-file-upload">
                        <Button
                          variant="outlined"
                          component="span"
                          color="primary"
                          style={{ marginBottom: 16 }}
                        >
                          Upload Postman Collection File
                        </Button>
                      </label>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        required
                        fullWidth
                        label="Collection Content (JSON)"
                        multiline
                        rows={10}
                        value={formState.content}
                        onChange={handleChange('content')}
                        error={!!errors.content}
                        helperText={errors.content || "Paste the exported Postman collection JSON here or use the upload button above"}
                        variant="outlined"
                      />
                      <Typography variant="caption" color="textSecondary">
                        To get your Postman collection JSON:
                        1. In Postman, click on the collection you want to export
                        2. Click the "..." (three dots) menu
                        3. Select "Export"
                        4. Choose "Collection v2.1" format
                        5. Save the file and upload it using the button above, or copy the JSON content and paste it here
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Button
                    type="button"
                    variant="outlined"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                    style={{ marginRight: 16 }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Saving...' : 'Save Collection'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </InfoCard>
        </Grid>
      </Grid>
    </Content>
  );
};
