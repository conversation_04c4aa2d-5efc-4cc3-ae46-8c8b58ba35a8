import React, { useState } from 'react';
import {
  Typography,
  Box,
  Paper,
  makeStyles,
  Tabs,
  Tab,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Divider,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ApiRequest, ApiEnvironment, HttpMethod } from '../../types';
import { CodeSnippet } from '@backstage/core-components';

// Icons
import SendIcon from '@material-ui/icons/Send';
import AddIcon from '@material-ui/icons/Add';
import DeleteIcon from '@material-ui/icons/Delete';
import SaveIcon from '@material-ui/icons/Save';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  urlBar: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  methodSelect: {
    width: 120,
    marginRight: theme.spacing(2),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(2),
  },
  sendButton: {
    marginLeft: theme.spacing(1),
  },
  tabPanel: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
  keyValueRow: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(1),
  },
  keyValueKey: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  keyValueValue: {
    flexGrow: 2,
    marginRight: theme.spacing(1),
  },
  keyValueActions: {
    display: 'flex',
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
  bodyEditor: {
    fontFamily: 'monospace',
    width: '100%',
    minHeight: '200px',
  },
  saveButton: {
    marginTop: theme.spacing(2),
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  const classes = useStyles();

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`request-tabpanel-${index}`}
      aria-labelledby={`request-tab-${index}`}
      className={classes.tabPanel}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

interface RequestPanelProps {
  request: ApiRequest;
  environment?: ApiEnvironment;
  onRequestChange: (request: ApiRequest) => void;
  onSendRequest: () => void;
  onSaveRequest: () => void;
  isLoading?: boolean;
  error?: string | null;
}

export const RequestPanel: React.FC<RequestPanelProps> = ({
  request,
  environment,
  onRequestChange,
  onSendRequest,
  onSaveRequest,
  isLoading = false,
  error = null,
}) => {
  const classes = useStyles();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMethodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onRequestChange({
      ...request,
      method: event.target.value as HttpMethod,
    });
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRequestChange({
      ...request,
      url: event.target.value,
    });
  };

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRequestChange({
      ...request,
      name: event.target.value,
    });
  };

  // Params tab handlers
  const handleAddParam = () => {
    onRequestChange({
      ...request,
      params: [...request.params, { key: '', value: '', enabled: true }],
    });
  };

  const handleRemoveParam = (index: number) => {
    const newParams = [...request.params];
    newParams.splice(index, 1);
    onRequestChange({
      ...request,
      params: newParams,
    });
  };

  const handleParamChange = (index: number, field: 'key' | 'value', value: string) => {
    const newParams = [...request.params];
    newParams[index][field] = value;
    onRequestChange({
      ...request,
      params: newParams,
    });
  };

  const handleParamToggle = (index: number) => {
    const newParams = [...request.params];
    newParams[index].enabled = !newParams[index].enabled;
    onRequestChange({
      ...request,
      params: newParams,
    });
  };

  // Headers tab handlers
  const handleAddHeader = () => {
    onRequestChange({
      ...request,
      headers: [...request.headers, { key: '', value: '', enabled: true }],
    });
  };

  const handleRemoveHeader = (index: number) => {
    const newHeaders = [...request.headers];
    newHeaders.splice(index, 1);
    onRequestChange({
      ...request,
      headers: newHeaders,
    });
  };

  const handleHeaderChange = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...request.headers];
    newHeaders[index][field] = value;
    onRequestChange({
      ...request,
      headers: newHeaders,
    });
  };

  const handleHeaderToggle = (index: number) => {
    const newHeaders = [...request.headers];
    newHeaders[index].enabled = !newHeaders[index].enabled;
    onRequestChange({
      ...request,
      headers: newHeaders,
    });
  };

  // Body tab handlers
  const handleBodyModeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onRequestChange({
      ...request,
      body: {
        ...request.body,
        mode: event.target.value as 'none' | 'raw' | 'form-data' | 'urlencoded',
      },
    });
  };

  const handleRawBodyChange = (value: string) => {
    onRequestChange({
      ...request,
      body: {
        ...request.body,
        raw: value,
      },
    });
  };

  return (
    <Paper className={classes.root}>
      <Box mb={2}>
        <TextField
          label="Request Name"
          variant="outlined"
          fullWidth
          value={request.name}
          onChange={handleNameChange}
          margin="normal"
        />
      </Box>

      <div className={classes.urlBar}>
        <FormControl variant="outlined" className={classes.methodSelect}>
          <Select
            value={request.method}
            onChange={handleMethodChange}
            variant="outlined"
          >
            <MenuItem value="GET">GET</MenuItem>
            <MenuItem value="POST">POST</MenuItem>
            <MenuItem value="PUT">PUT</MenuItem>
            <MenuItem value="DELETE">DELETE</MenuItem>
            <MenuItem value="PATCH">PATCH</MenuItem>
            <MenuItem value="HEAD">HEAD</MenuItem>
            <MenuItem value="OPTIONS">OPTIONS</MenuItem>
          </Select>
        </FormControl>
        <TextField
          className={classes.urlField}
          variant="outlined"
          placeholder="Enter request URL"
          value={request.url}
          onChange={handleUrlChange}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={onSendRequest}
          disabled={isLoading || !request.url}
        >
          Send
        </Button>
      </div>

      {error && (
        <Alert severity="error" style={{ marginBottom: '16px' }}>
          {error}
        </Alert>
      )}

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Params" />
        <Tab label="Headers" />
        <Tab label="Body" />
        <Tab label="Auth" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        {request.params.map((param, index) => (
          <div key={index} className={classes.keyValueRow}>
            <TextField
              className={classes.keyValueKey}
              label="Key"
              value={param.key}
              onChange={(e) => handleParamChange(index, 'key', e.target.value)}
              variant="outlined"
              size="small"
              disabled={!param.enabled}
            />
            <TextField
              className={classes.keyValueValue}
              label="Value"
              value={param.value}
              onChange={(e) => handleParamChange(index, 'value', e.target.value)}
              variant="outlined"
              size="small"
              disabled={!param.enabled}
            />
            <div className={classes.keyValueActions}>
              <Tooltip title={param.enabled ? 'Disable' : 'Enable'}>
                <IconButton size="small" onClick={() => handleParamToggle(index)}>
                  {param.enabled ? <span>✓</span> : <span>✗</span>}
                </IconButton>
              </Tooltip>
              <Tooltip title="Remove">
                <IconButton size="small" onClick={() => handleRemoveParam(index)}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </div>
          </div>
        ))}
        <Button
          startIcon={<AddIcon />}
          onClick={handleAddParam}
          className={classes.addButton}
        >
          Add Parameter
        </Button>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Headers tab content */}
        {request.headers.map((header, index) => (
          <div key={index} className={classes.keyValueRow}>
            <TextField
              className={classes.keyValueKey}
              label="Key"
              value={header.key}
              onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
              variant="outlined"
              size="small"
              disabled={!header.enabled}
            />
            <TextField
              className={classes.keyValueValue}
              label="Value"
              value={header.value}
              onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
              variant="outlined"
              size="small"
              disabled={!header.enabled}
            />
            <div className={classes.keyValueActions}>
              <Tooltip title={header.enabled ? 'Disable' : 'Enable'}>
                <IconButton size="small" onClick={() => handleHeaderToggle(index)}>
                  {header.enabled ? <span>✓</span> : <span>✗</span>}
                </IconButton>
              </Tooltip>
              <Tooltip title="Remove">
                <IconButton size="small" onClick={() => handleRemoveHeader(index)}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </div>
          </div>
        ))}
        <Button
          startIcon={<AddIcon />}
          onClick={handleAddHeader}
          className={classes.addButton}
        >
          Add Header
        </Button>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Body tab content */}
        <FormControl fullWidth margin="normal">
          <Select
            value={request.body.mode}
            onChange={handleBodyModeChange}
            displayEmpty
          >
            <MenuItem value="none">None</MenuItem>
            <MenuItem value="raw">Raw</MenuItem>
            <MenuItem value="form-data">Form Data</MenuItem>
            <MenuItem value="urlencoded">URL Encoded</MenuItem>
          </Select>
        </FormControl>

        {request.body.mode === 'raw' && (
          <Box mt={2} className={classes.bodyEditor}>
            <CodeSnippet
              text={request.body.raw || ''}
              language="json"
              showLineNumbers
              showCopyCodeButton
              onChange={handleRawBodyChange}
            />
          </Box>
        )}

        {/* Form data and URL encoded implementations would go here */}
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Auth tab content */}
        <Typography variant="body1">
          Authentication settings will be implemented in a future update.
        </Typography>
      </TabPanel>

      <Divider style={{ marginTop: '16px' }} />
      
      <Box display="flex" justifyContent="flex-end" mt={2}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={onSaveRequest}
          className={classes.saveButton}
        >
          Save Request
        </Button>
      </Box>
    </Paper>
  );
};
