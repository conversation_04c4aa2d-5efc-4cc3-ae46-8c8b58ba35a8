import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Paper,
  makeStyles,
  CircularProgress,
  Tooltip,
  IconButton,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { CodeSnippet } from '@backstage/core-components';
import { ApiRequest } from '../../types';

// Icons
import SaveIcon from '@material-ui/icons/Save';
import DeleteIcon from '@material-ui/icons/Delete';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  codeEditor: {
    fontFamily: 'monospace',
    width: '100%',
    minHeight: '200px',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: theme.spacing(2),
  },
  leftButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
  rightButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
}));

interface PreRequestScriptPanelProps {
  request: ApiRequest;
  onSaveScript: (script: string) => void;
  isSaving?: boolean;
  error?: string | null;
}

export const PreRequestScriptPanel: React.FC<PreRequestScriptPanelProps> = ({
  request,
  onSaveScript,
  isSaving = false,
  error = null,
}) => {
  const classes = useStyles();
  const [script, setScript] = useState<string>('');
  const [isEdited, setIsEdited] = useState<boolean>(false);

  // Update script when request changes
  useEffect(() => {
    if (request.id && request.preRequestScript) {
      setScript(request.preRequestScript);
      setIsEdited(false);
    } else {
      setScript('');
      setIsEdited(false);
    }
  }, [request.id, request.preRequestScript]);

  const handleSaveScript = () => {
    onSaveScript(script);
    setIsEdited(false);
  };

  const handleClearScript = () => {
    setScript('');
    setIsEdited(true);
  };

  const handleScriptChange = (newScript: string) => {
    setScript(newScript);
    setIsEdited(true);
  };

  return (
    <Paper className={classes.root}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Pre-request Script</Typography>
      </Box>

      <Typography variant="body2" color="textSecondary" paragraph>
        Write scripts to execute before the request is sent
      </Typography>

      {error && (
        <Alert severity="error" style={{ marginBottom: '16px' }}>
          {error}
        </Alert>
      )}

      <Box className={classes.codeEditor}>
        <CodeSnippet
          text={script}
          language="javascript"
          showLineNumbers
          showCopyCodeButton
          customStyle={{ minHeight: '200px' }}
          onChange={handleScriptChange}
        />
      </Box>

      <Box className={classes.buttonContainer}>
        <div className={classes.leftButtons}>
          <Button
            variant="outlined"
            color="default"
            onClick={handleClearScript}
            disabled={!script}
            startIcon={<DeleteIcon />}
          >
            Clear
          </Button>
        </div>
        <div className={classes.rightButtons}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleSaveScript}
            disabled={!isEdited || isSaving}
            startIcon={isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </Box>
    </Paper>
  );
};
