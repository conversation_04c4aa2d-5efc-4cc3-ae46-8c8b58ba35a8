import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ApiCollection, ApiFolder } from '../../types';

interface CreateFolderDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateFolder: (folderName: string, parentId: string | null, collectionId: string) => void;
  collections: ApiCollection[];
  selectedCollectionId?: string;
  selectedFolderId?: string;
}

export const CreateFolderDialog: React.FC<CreateFolderDialogProps> = ({
  open,
  onClose,
  onCreateFolder,
  collections,
  selectedCollectionId,
  selectedFolderId,
}) => {
  const [folderName, setFolderName] = useState('');
  const [collectionId, setCollectionId] = useState<string>(selectedCollectionId || '');
  const [parentFolderId, setParentFolderId] = useState<string | null>(selectedFolderId || null);
  const [error, setError] = useState<string | null>(null);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setFolderName('');
      setCollectionId(selectedCollectionId || (collections.length > 0 ? collections[0].id : ''));
      setParentFolderId(selectedFolderId || null);
      setError(null);
    }
  }, [open, selectedCollectionId, selectedFolderId, collections]);

  const handleSubmit = () => {
    if (!folderName.trim()) {
      setError('Folder name is required');
      return;
    }

    if (!collectionId) {
      setError('Collection is required');
      return;
    }

    onCreateFolder(folderName.trim(), parentFolderId, collectionId);
    onClose();
  };

  // Get all folders for the selected collection
  const getFolders = (collection?: ApiCollection): { id: string; name: string; level: number }[] => {
    if (!collection) return [];

    const result: { id: string; name: string; level: number }[] = [];

    const addFoldersRecursively = (folders: ApiFolder[], level: number) => {
      folders.forEach(folder => {
        result.push({
          id: folder.id,
          name: folder.name,
          level,
        });
        if (folder.folders.length > 0) {
          addFoldersRecursively(folder.folders, level + 1);
        }
      });
    };

    addFoldersRecursively(collection.folders, 0);
    return result;
  };

  const selectedCollection = collections.find(c => c.id === collectionId);
  const folders = getFolders(selectedCollection);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Create New Folder</DialogTitle>
      <DialogContent>
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Create a new folder to organize your requests
          </Typography>
        </Box>

        <TextField
          autoFocus
          margin="dense"
          id="folder-name"
          label="Folder Name"
          type="text"
          fullWidth
          value={folderName}
          onChange={(e) => setFolderName(e.target.value)}
          error={!!error && !folderName.trim()}
          helperText={error && !folderName.trim() ? error : ''}
        />

        <FormControl fullWidth margin="normal">
          <InputLabel id="collection-select-label">Collection</InputLabel>
          <Select
            labelId="collection-select-label"
            id="collection-select"
            value={collectionId}
            onChange={(e) => {
              setCollectionId(e.target.value as string);
              setParentFolderId(null); // Reset parent folder when collection changes
            }}
            disabled={!!selectedCollectionId} // Disable if collection is pre-selected
          >
            {collections.map(collection => (
              <MenuItem key={collection.id} value={collection.id}>
                {collection.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {folders.length > 0 && (
          <FormControl fullWidth margin="normal">
            <InputLabel id="parent-folder-select-label">Parent Folder (Optional)</InputLabel>
            <Select
              labelId="parent-folder-select-label"
              id="parent-folder-select"
              value={parentFolderId || ''}
              onChange={(e) => setParentFolderId(e.target.value as string || null)}
              displayEmpty
            >
              <MenuItem value="">
                <em>None (Root level)</em>
              </MenuItem>
              {folders.map(folder => (
                <MenuItem key={folder.id} value={folder.id}>
                  {'\u00A0'.repeat(folder.level * 2)}{folder.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {error && folderName.trim() && (
          <Box mt={2}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="default">
          Cancel
        </Button>
        <Button onClick={handleSubmit} color="primary" variant="contained">
          Create
        </Button>
      </DialogActions>
    </Dialog>
  );
};
