import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ImportExportService } from '../../services/ImportExportService';
import { ApiCollection, ApiEnvironment } from '../../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`import-tabpanel-${index}`}
      aria-labelledby={`import-tab-${index}`}
      {...other}
    >
      {value === index && <Box p={2}>{children}</Box>}
    </div>
  );
};

interface ImportDialogProps {
  open: boolean;
  onClose: () => void;
  onImportCollection: (collection: ApiCollection) => void;
  onImportEnvironment: (environment: ApiEnvironment) => void;
}

export const ImportDialog: React.FC<ImportDialogProps> = ({
  open,
  onClose,
  onImportCollection,
  onImportEnvironment,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [importMethod, setImportMethod] = useState<'file' | 'text'>('file');
  const [importText, setImportText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
    setError(null);
  };

  const handleImportMethodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setImportMethod(event.target.value as 'file' | 'text');
    setError(null);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
      setError(null);
    }
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setImportText(event.target.value);
    setError(null);
  };

  const handleImport = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let jsonData: any;

      if (importMethod === 'file') {
        if (!selectedFile) {
          throw new Error('Please select a file to import');
        }

        const fileContent = await selectedFile.text();
        jsonData = JSON.parse(fileContent);
      } else {
        if (!importText.trim()) {
          throw new Error('Please enter JSON data to import');
        }

        jsonData = JSON.parse(importText);
      }

      if (tabValue === 0) {
        // Import collection
        if (!jsonData.info || !jsonData.item) {
          throw new Error('Invalid Postman collection format');
        }

        const collection = ImportExportService.importPostmanCollection(jsonData);
        onImportCollection(collection);
      } else {
        // Import environment
        if (!jsonData.name || !jsonData.values) {
          throw new Error('Invalid Postman environment format');
        }

        const environment = ImportExportService.importPostmanEnvironment(jsonData);
        onImportEnvironment(environment);
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Import</DialogTitle>
      <DialogContent>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Collection" />
          <Tab label="Environment" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="body1" gutterBottom>
            Import a Postman collection in v2.1 format
          </Typography>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="body1" gutterBottom>
            Import a Postman environment
          </Typography>
        </TabPanel>

        <Box mt={2}>
          <FormControl fullWidth variant="outlined" margin="normal">
            <InputLabel id="import-method-label">Import Method</InputLabel>
            <Select
              labelId="import-method-label"
              id="import-method"
              value={importMethod}
              onChange={handleImportMethodChange}
              label="Import Method"
            >
              <MenuItem value="file">From File</MenuItem>
              <MenuItem value="text">Raw Text</MenuItem>
            </Select>
          </FormControl>

          {importMethod === 'file' ? (
            <Box mt={2}>
              <input
                accept="application/json"
                style={{ display: 'none' }}
                id="import-file"
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="import-file">
                <Button variant="contained" color="primary" component="span">
                  Select File
                </Button>
              </label>
              {selectedFile && (
                <Typography variant="body2" style={{ marginTop: 8 }}>
                  Selected file: {selectedFile.name}
                </Typography>
              )}
            </Box>
          ) : (
            <TextField
              label="JSON Data"
              multiline
              rows={10}
              variant="outlined"
              fullWidth
              margin="normal"
              value={importText}
              onChange={handleTextChange}
              placeholder={
                tabValue === 0
                  ? '{"info": {"name": "My Collection"}, "item": [...]}'
                  : '{"name": "My Environment", "values": [...]}'
              }
            />
          )}

          {error && (
            <Box mt={2}>
              <Alert severity="error">{error}</Alert>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="default">
          Cancel
        </Button>
        <Button
          onClick={handleImport}
          color="primary"
          variant="contained"
          disabled={isLoading || (importMethod === 'file' && !selectedFile) || (importMethod === 'text' && !importText.trim())}
        >
          {isLoading ? <CircularProgress size={24} /> : 'Import'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
