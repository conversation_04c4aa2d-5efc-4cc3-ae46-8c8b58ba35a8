import React from 'react';
import {
  Typo<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Checkbox,
  Button,
  makeStyles,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import DeleteIcon from '@material-ui/icons/Delete';

const useStyles = makeStyles(theme => ({
  table: {
    minWidth: 650,
  },
  tableContainer: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
}));

interface ParamsTabProps {
  params: { key: string; value: string; enabled: boolean }[];
  onChange: (params: { key: string; value: string; enabled: boolean }[]) => void;
}

export const ParamsTab: React.FC<ParamsTabProps> = ({ params, onChange }) => {
  const classes = useStyles();

  const handleParamChange = (index: number, field: 'key' | 'value', value: string) => {
    const updatedParams = [...params];
    updatedParams[index] = {
      ...updatedParams[index],
      [field]: value,
    };
    onChange(updatedParams);
  };

  const handleToggleParam = (index: number) => {
    const updatedParams = [...params];
    updatedParams[index] = {
      ...updatedParams[index],
      enabled: !updatedParams[index].enabled,
    };
    onChange(updatedParams);
  };

  const handleAddParam = () => {
    onChange([...params, { key: '', value: '', enabled: true }]);
  };

  const handleDeleteParam = (index: number) => {
    const updatedParams = [...params];
    updatedParams.splice(index, 1);
    onChange(updatedParams);
  };

  return (
    <div>
      <Typography variant="subtitle2" gutterBottom>
        Query Parameters
      </Typography>
      <div className={classes.tableContainer}>
        <Table className={classes.table} size="small">
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox"></TableCell>
              <TableCell>Key</TableCell>
              <TableCell>Value</TableCell>
              <TableCell width="50"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {params.map((param, index) => (
              <TableRow key={index}>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={param.enabled}
                    onChange={() => handleToggleParam(index)}
                    color="primary"
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={param.key}
                    onChange={(e) => handleParamChange(index, 'key', e.target.value)}
                    placeholder="Parameter name"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={param.value}
                    onChange={(e) => handleParamChange(index, 'value', e.target.value)}
                    placeholder="Parameter value"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteParam(index)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {params.length === 0 && (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  No parameters defined
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<AddIcon />}
        onClick={handleAddParam}
        className={classes.addButton}
      >
        Add Parameter
      </Button>
    </div>
  );
};
