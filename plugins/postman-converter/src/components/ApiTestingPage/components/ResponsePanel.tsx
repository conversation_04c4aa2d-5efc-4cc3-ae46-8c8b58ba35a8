import React from 'react';
import {
  Typography,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  Box,
  makeStyles,
} from '@material-ui/core';
import { CodeSnippet } from '@backstage/core-components';
import { TabPanel } from './TabPanel';
import { ApiResponse } from '../../../types';

const useStyles = makeStyles(theme => ({
  responsePanel: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
  },
  statusChip: {
    marginLeft: theme.spacing(1),
  },
  responseTime: {
    marginLeft: theme.spacing(1),
    color: theme.palette.text.secondary,
  },
  tabContent: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
  table: {
    minWidth: 650,
  },
}));

interface ResponsePanelProps {
  response: ApiResponse;
  tabValue: number;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
}

export const ResponsePanel: React.FC<ResponsePanelProps> = ({
  response,
  tabValue,
  onTabChange,
}) => {
  const classes = useStyles();

  // Function to determine status color
  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) {
      return '#4caf50'; // Green for success
    } else if (status >= 300 && status < 400) {
      return '#ff9800'; // Orange for redirection
    } else if (status >= 400 && status < 500) {
      return '#f44336'; // Red for client errors
    } else if (status >= 500) {
      return '#9c27b0'; // Purple for server errors
    }
    return '#757575'; // Grey for unknown
  };

  // Function to format response body based on content type
  const formatResponseBody = (body: string, contentType?: string): { formatted: string; language: string } => {
    if (!body) {
      return { formatted: '', language: 'text' };
    }

    if (contentType?.includes('application/json')) {
      try {
        const parsed = JSON.parse(body);
        return {
          formatted: JSON.stringify(parsed, null, 2),
          language: 'json',
        };
      } catch (e) {
        // If parsing fails, return as is
        return { formatted: body, language: 'text' };
      }
    } else if (contentType?.includes('text/html')) {
      return { formatted: body, language: 'html' };
    } else if (contentType?.includes('text/xml') || contentType?.includes('application/xml')) {
      return { formatted: body, language: 'xml' };
    }

    return { formatted: body, language: 'text' };
  };

  const { formatted, language } = formatResponseBody(
    response.body,
    response.headers['content-type']
  );

  return (
    <Paper className={classes.responsePanel}>
      <Box display="flex" alignItems="center" mb={2}>
        <Typography variant="h6">
          Response
          <Chip
            label={`${response.status} ${response.statusText}`}
            className={classes.statusChip}
            style={{ backgroundColor: getStatusColor(response.status) }}
          />
          <span className={classes.responseTime}>{response.time}ms</span>
        </Typography>
      </Box>

      <Tabs
        value={tabValue}
        onChange={onTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Body" />
        <Tab label="Headers" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        <CodeSnippet
          text={formatted}
          language={language}
          showLineNumbers
          showCopyCodeButton
          customStyle={{ minHeight: '200px' }}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Table className={classes.table} size="small">
          <TableHead>
            <TableRow>
              <TableCell>Key</TableCell>
              <TableCell>Value</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(response.headers).map(([key, value]) => (
              <TableRow key={key}>
                <TableCell>{key}</TableCell>
                <TableCell>{value}</TableCell>
              </TableRow>
            ))}
            {Object.keys(response.headers).length === 0 && (
              <TableRow>
                <TableCell colSpan={2} align="center">
                  No headers
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TabPanel>
    </Paper>
  );
};
