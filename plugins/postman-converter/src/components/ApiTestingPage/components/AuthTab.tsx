import React from 'react';
import {
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
  makeStyles,
} from '@material-ui/core';
import { ApiRequest } from '../../../types';

const useStyles = makeStyles(theme => ({
  formControl: {
    marginBottom: theme.spacing(2),
    minWidth: 200,
  },
}));

interface AuthTabProps {
  auth?: ApiRequest['auth'];
  onChange: (auth: ApiRequest['auth']) => void;
}

export const AuthTab: React.FC<AuthTabProps> = ({ auth = { type: 'none' }, onChange }) => {
  const classes = useStyles();

  const handleAuthTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const authType = event.target.value as 'none' | 'basic' | 'bearer' | 'apiKey';
    let newAuth: ApiRequest['auth'];

    if (authType === 'none') {
      newAuth = { type: 'none' };
    } else if (authType === 'basic') {
      newAuth = {
        type: 'basic',
        basic: { username: '', password: '' }
      };
    } else if (authType === 'bearer') {
      newAuth = {
        type: 'bearer',
        bearer: { token: '' }
      };
    } else if (authType === 'apiKey') {
      newAuth = {
        type: 'apiKey',
        apiKey: { key: '', value: '', in: 'header' }
      };
    } else {
      newAuth = { type: 'none' };
    }

    onChange(newAuth);
  };

  const handleBasicAuthChange = (field: 'username' | 'password', value: string) => {
    if (auth.type !== 'basic' || !auth.basic) return;
    
    onChange({
      ...auth,
      basic: {
        ...auth.basic,
        [field]: value
      }
    });
  };

  const handleBearerTokenChange = (value: string) => {
    if (auth.type !== 'bearer' || !auth.bearer) return;
    
    onChange({
      ...auth,
      bearer: {
        token: value
      }
    });
  };

  const handleApiKeyChange = (field: 'key' | 'value', value: string) => {
    if (auth.type !== 'apiKey' || !auth.apiKey) return;
    
    onChange({
      ...auth,
      apiKey: {
        ...auth.apiKey,
        [field]: value
      }
    });
  };

  const handleApiKeyInChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    if (auth.type !== 'apiKey' || !auth.apiKey) return;
    
    onChange({
      ...auth,
      apiKey: {
        ...auth.apiKey,
        in: event.target.value as 'header' | 'query'
      }
    });
  };

  return (
    <div>
      <Typography variant="subtitle2" gutterBottom>
        Authentication
      </Typography>
      <div>
        <FormControl className={classes.formControl} fullWidth>
          <InputLabel id="auth-type-select-label">Auth Type</InputLabel>
          <Select
            labelId="auth-type-select-label"
            id="auth-type-select"
            value={auth.type}
            onChange={handleAuthTypeChange}
          >
            <MenuItem value="none">No Auth</MenuItem>
            <MenuItem value="basic">Basic Auth</MenuItem>
            <MenuItem value="bearer">Bearer Token</MenuItem>
            <MenuItem value="apiKey">API Key</MenuItem>
          </Select>
        </FormControl>

        {auth.type === 'basic' && (
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Basic Auth
            </Typography>
            <TextField
              label="Username"
              fullWidth
              margin="normal"
              value={auth.basic?.username || ''}
              onChange={(e) => handleBasicAuthChange('username', e.target.value)}
            />
            <TextField
              label="Password"
              fullWidth
              margin="normal"
              type="password"
              value={auth.basic?.password || ''}
              onChange={(e) => handleBasicAuthChange('password', e.target.value)}
            />
          </Box>
        )}

        {auth.type === 'bearer' && (
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Bearer Token
            </Typography>
            <TextField
              label="Token"
              fullWidth
              margin="normal"
              value={auth.bearer?.token || ''}
              onChange={(e) => handleBearerTokenChange(e.target.value)}
            />
          </Box>
        )}

        {auth.type === 'apiKey' && (
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              API Key
            </Typography>
            <TextField
              label="Key"
              fullWidth
              margin="normal"
              value={auth.apiKey?.key || ''}
              onChange={(e) => handleApiKeyChange('key', e.target.value)}
            />
            <TextField
              label="Value"
              fullWidth
              margin="normal"
              value={auth.apiKey?.value || ''}
              onChange={(e) => handleApiKeyChange('value', e.target.value)}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel id="api-key-in-label">Add to</InputLabel>
              <Select
                labelId="api-key-in-label"
                value={auth.apiKey?.in || 'header'}
                onChange={handleApiKeyInChange}
              >
                <MenuItem value="header">Header</MenuItem>
                <MenuItem value="query">Query Parameter</MenuItem>
              </Select>
            </FormControl>
          </Box>
        )}
      </div>
    </div>
  );
};
