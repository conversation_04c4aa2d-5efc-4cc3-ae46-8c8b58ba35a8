import React from 'react';
import {
  Typo<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Checkbox,
  Button,
  makeStyles,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import DeleteIcon from '@material-ui/icons/Delete';

const useStyles = makeStyles(theme => ({
  table: {
    minWidth: 650,
  },
  tableContainer: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
}));

interface HeadersTabProps {
  headers: { key: string; value: string; enabled: boolean }[];
  onChange: (headers: { key: string; value: string; enabled: boolean }[]) => void;
}

export const HeadersTab: React.FC<HeadersTabProps> = ({ headers, onChange }) => {
  const classes = useStyles();

  const handleHeaderChange = (index: number, field: 'key' | 'value', value: string) => {
    const updatedHeaders = [...headers];
    updatedHeaders[index] = {
      ...updatedHeaders[index],
      [field]: value,
    };
    onChange(updatedHeaders);
  };

  const handleToggleHeader = (index: number) => {
    const updatedHeaders = [...headers];
    updatedHeaders[index] = {
      ...updatedHeaders[index],
      enabled: !updatedHeaders[index].enabled,
    };
    onChange(updatedHeaders);
  };

  const handleAddHeader = () => {
    onChange([...headers, { key: '', value: '', enabled: true }]);
  };

  const handleDeleteHeader = (index: number) => {
    const updatedHeaders = [...headers];
    updatedHeaders.splice(index, 1);
    onChange(updatedHeaders);
  };

  return (
    <div>
      <Typography variant="subtitle2" gutterBottom>
        Headers
      </Typography>
      <div className={classes.tableContainer}>
        <Table className={classes.table} size="small">
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox"></TableCell>
              <TableCell>Key</TableCell>
              <TableCell>Value</TableCell>
              <TableCell width="50"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {headers.map((header, index) => (
              <TableRow key={index}>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={header.enabled}
                    onChange={() => handleToggleHeader(index)}
                    color="primary"
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={header.key}
                    onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
                    placeholder="Header name"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={header.value}
                    onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
                    placeholder="Header value"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteHeader(index)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {headers.length === 0 && (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  No headers defined
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<AddIcon />}
        onClick={handleAddHeader}
        className={classes.addButton}
      >
        Add Header
      </Button>
    </div>
  );
};
