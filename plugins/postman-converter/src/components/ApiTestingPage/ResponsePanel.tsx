import React, { useState } from 'react';
import {
  Typography,
  Box,
  Paper,
  makeStyles,
  Tabs,
  Tab,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Divider,
  Button,
} from '@material-ui/core';
import { CodeSnippet } from '@backstage/core-components';
import { ApiResponse } from '../../types';

// Icons
import CodeIcon from '@material-ui/icons/Code';
import TableChartIcon from '@material-ui/icons/TableChart';
import TimerIcon from '@material-ui/icons/Timer';
import FileCopyIcon from '@material-ui/icons/FileCopy';
import HttpIcon from '@material-ui/icons/Http';
import InfoIcon from '@material-ui/icons/Info';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
    marginTop: theme.spacing(2),
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2),
  },
  statusInfo: {
    display: 'flex',
    alignItems: 'center',
  },
  statusSuccess: {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
  },
  statusError: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
  },
  statusInfo2xx: {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
  },
  statusInfo3xx: {
    backgroundColor: theme.palette.info.main,
    color: theme.palette.info.contrastText,
  },
  statusInfo4xx: {
    backgroundColor: theme.palette.warning.main,
    color: theme.palette.warning.contrastText,
  },
  statusInfo5xx: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
  },
  responseTime: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  responseSize: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  requestInfo: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  tabPanel: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
  bodyContent: {
    maxHeight: '400px',
    overflow: 'auto',
  },
  copyButton: {
    marginLeft: theme.spacing(1),
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  const classes = useStyles();

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`response-tabpanel-${index}`}
      aria-labelledby={`response-tab-${index}`}
      className={classes.tabPanel}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

interface ResponsePanelProps {
  response: ApiResponse | null;
}

export const ResponsePanel: React.FC<ResponsePanelProps> = ({ response }) => {
  const classes = useStyles();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusClass = (status: number): string => {
    if (status >= 200 && status < 300) {
      return classes.statusInfo2xx;
    } else if (status >= 300 && status < 400) {
      return classes.statusInfo3xx;
    } else if (status >= 400 && status < 500) {
      return classes.statusInfo4xx;
    } else if (status >= 500) {
      return classes.statusInfo5xx;
    }
    return '';
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (!response) {
    return null;
  }

  return (
    <Paper className={classes.root}>
      <div className={classes.header}>
        <div className={classes.statusInfo}>
          <Chip
            label={`${response.status} ${response.statusText}`}
            className={getStatusClass(response.status)}
          />
          <div className={classes.responseTime}>
            <TimerIcon fontSize="small" style={{ marginRight: '4px' }} />
            {response.time} ms
          </div>
          <div className={classes.responseSize}>
            <FileCopyIcon fontSize="small" style={{ marginRight: '4px' }} />
            {formatBytes(response.size)}
          </div>
          <div className={classes.requestInfo}>
            <HttpIcon fontSize="small" style={{ marginRight: '4px' }} />
            Direct
          </div>
        </div>
        <Button
          size="small"
          startIcon={<FileCopyIcon />}
          onClick={() => copyToClipboard(response.body)}
          className={classes.copyButton}
        >
          Copy Response
        </Button>
      </div>

      <Divider />

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab icon={<CodeIcon />} label="Body" />
        <Tab icon={<TableChartIcon />} label="Headers" />
        <Tab icon={<InfoIcon />} label="Info" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        <div className={classes.bodyContent}>
          {(() => {
            let language = "text";
            if (response.headers['content-type']?.includes('application/json')) {
              language = "json";
            } else if (response.headers['content-type']?.includes('text/html')) {
              language = "html";
            } else if (response.headers['content-type']?.includes('text/xml') ||
                       response.headers['content-type']?.includes('application/xml')) {
              language = "xml";
            }
            return (
              <CodeSnippet
                text={response.body}
                language={language}
                showLineNumbers
                showCopyCodeButton
              />
            );
          })()}
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>Header</strong></TableCell>
              <TableCell><strong>Value</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(response.headers).map(([key, value]) => (
              <TableRow key={key}>
                <TableCell>{key}</TableCell>
                <TableCell>{value}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>Request Information</Typography>
        <Table size="small">
          <TableBody>
            <TableRow>
              <TableCell><strong>Request URL</strong></TableCell>
              <TableCell>{response.url || 'Unknown'}</TableCell>
            </TableRow>
            {/* <TableRow>
              <TableCell><strong>Request Type</strong></TableCell>
              <TableCell>Direct (no proxy)</TableCell>
            </TableRow> */}
            <TableRow>
              <TableCell><strong>Response Status</strong></TableCell>
              <TableCell>{response.status} {response.statusText}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell><strong>Response Time</strong></TableCell>
              <TableCell>{response.time} ms</TableCell>
            </TableRow>
            <TableRow>
              <TableCell><strong>Response Size</strong></TableCell>
              <TableCell>{formatBytes(response.size)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TabPanel>
    </Paper>
  );
};
