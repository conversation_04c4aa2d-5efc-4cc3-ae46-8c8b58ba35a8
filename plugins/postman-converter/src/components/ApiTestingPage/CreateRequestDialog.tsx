import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { ApiCollection, ApiFolder, HttpMethod } from '../../types';

interface CreateRequestDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateRequest: (
    requestName: string, 
    method: HttpMethod, 
    url: string, 
    parentId: string | null, 
    collectionId: string
  ) => void;
  collections: ApiCollection[];
  selectedCollectionId?: string;
  selectedFolderId?: string;
}

export const CreateRequestDialog: React.FC<CreateRequestDialogProps> = ({
  open,
  onClose,
  onCreateRequest,
  collections,
  selectedCollectionId,
  selectedFolderId,
}) => {
  const [requestName, setRequestName] = useState('');
  const [method, setMethod] = useState<HttpMethod>('GET');
  const [url, setUrl] = useState('');
  const [collectionId, setCollectionId] = useState<string>(selectedCollectionId || '');
  const [parentId, setParentId] = useState<string | null>(selectedFolderId || null);
  const [error, setError] = useState<string | null>(null);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setRequestName('');
      setMethod('GET');
      setUrl('');
      setCollectionId(selectedCollectionId || (collections.length > 0 ? collections[0].id : ''));
      setParentId(selectedFolderId || null);
      setError(null);
    }
  }, [open, selectedCollectionId, selectedFolderId, collections]);

  const handleSubmit = () => {
    if (!requestName.trim()) {
      setError('Request name is required');
      return;
    }

    if (!collectionId) {
      setError('Collection is required');
      return;
    }

    onCreateRequest(requestName.trim(), method, url, parentId, collectionId);
    onClose();
  };

  // Get all folders for the selected collection
  const getFoldersAndCollection = (collection?: ApiCollection): { id: string; name: string; level: number; isCollection: boolean }[] => {
    if (!collection) return [];

    const result: { id: string; name: string; level: number; isCollection: boolean }[] = [
      { id: collection.id, name: collection.name, level: 0, isCollection: true }
    ];

    const addFoldersRecursively = (folders: ApiFolder[], level: number) => {
      folders.forEach(folder => {
        result.push({
          id: folder.id,
          name: folder.name,
          level,
          isCollection: false,
        });
        if (folder.folders.length > 0) {
          addFoldersRecursively(folder.folders, level + 1);
        }
      });
    };

    addFoldersRecursively(collection.folders, 1);
    return result;
  };

  const selectedCollection = collections.find(c => c.id === collectionId);
  const foldersAndCollection = getFoldersAndCollection(selectedCollection);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Create New Request</DialogTitle>
      <DialogContent>
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Create a new API request
          </Typography>
        </Box>

        <TextField
          autoFocus
          margin="dense"
          id="request-name"
          label="Request Name"
          type="text"
          fullWidth
          value={requestName}
          onChange={(e) => setRequestName(e.target.value)}
          error={!!error && !requestName.trim()}
          helperText={error && !requestName.trim() ? error : ''}
        />

        <Box display="flex" mt={2} mb={1}>
          <FormControl style={{ width: '150px', marginRight: '16px' }}>
            <InputLabel id="method-select-label">Method</InputLabel>
            <Select
              labelId="method-select-label"
              id="method-select"
              value={method}
              onChange={(e) => setMethod(e.target.value as HttpMethod)}
            >
              <MenuItem value="GET">GET</MenuItem>
              <MenuItem value="POST">POST</MenuItem>
              <MenuItem value="PUT">PUT</MenuItem>
              <MenuItem value="DELETE">DELETE</MenuItem>
              <MenuItem value="PATCH">PATCH</MenuItem>
              <MenuItem value="HEAD">HEAD</MenuItem>
              <MenuItem value="OPTIONS">OPTIONS</MenuItem>
            </Select>
          </FormControl>

          <TextField
            id="request-url"
            label="URL (Optional)"
            type="text"
            fullWidth
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://api.example.com/endpoint"
          />
        </Box>

        <FormControl fullWidth margin="normal">
          <InputLabel id="collection-select-label">Collection</InputLabel>
          <Select
            labelId="collection-select-label"
            id="collection-select"
            value={collectionId}
            onChange={(e) => {
              setCollectionId(e.target.value as string);
              setParentId(null); // Reset parent when collection changes
            }}
            disabled={!!selectedCollectionId} // Disable if collection is pre-selected
          >
            {collections.map(collection => (
              <MenuItem key={collection.id} value={collection.id}>
                {collection.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {foldersAndCollection.length > 0 && (
          <FormControl fullWidth margin="normal">
            <InputLabel id="parent-select-label">Location</InputLabel>
            <Select
              labelId="parent-select-label"
              id="parent-select"
              value={parentId || collectionId}
              onChange={(e) => setParentId(e.target.value as string)}
            >
              {foldersAndCollection.map(item => (
                <MenuItem key={item.id} value={item.id}>
                  {'\u00A0'.repeat(item.level * 2)}{item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {error && requestName.trim() && (
          <Box mt={2}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="default">
          Cancel
        </Button>
        <Button onClick={handleSubmit} color="primary" variant="contained">
          Create
        </Button>
      </DialogActions>
    </Dialog>
  );
};
