import { useState, useCallback } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { ApiCollection, Collection } from '../../../types';
import { postmanConverterApiRef } from '../../../api';
import { convertToApiCollection } from '../utils/collectionUtils';

export const useCollections = () => {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  const [collections, setCollections] = useState<ApiCollection[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  // Fetch collections from the API
  const { loading: collectionsLoading, error: collectionsError } = useAsync(async () => {
    try {
      const fetchedCollections = await postmanConverterApi.getCollections();

      // Convert backend collections to frontend ApiCollection format
      const apiCollections: ApiCollection[] = [];

      for (const collection of fetchedCollections) {
        const apiCollection = await convertToApiCollection(collection, errorApi, postmanConverterApi);
        apiCollections.push(apiCollection);
      }

      // Update collections state
      if (apiCollections.length > 0) {
        setCollections(apiCollections);
      }

      return fetchedCollections;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      errorApi.post(error);
      throw error;
    }
  }, []);

  // Handle folder toggle
  const handleFolderToggle = useCallback((folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    setSelectedItemId(itemId);
  }, []);

  // Handle add collection
  const handleAddCollection = useCallback(async () => {
    // Create a basic empty Postman collection structure
    const emptyCollection = {
      info: {
        name: 'New Collection',
        description: 'A new collection',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: []
    };

    try {
      // Create a new collection in the database
      const newCollection = await postmanConverterApi.createCollection({
        name: 'New Collection',
        description: 'A new collection',
        content: JSON.stringify(emptyCollection)
      });

      // Convert to ApiCollection format
      const apiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, apiCollection]);

      return { success: true, collection: apiCollection };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  // Handle delete collection
  const handleDeleteCollection = useCallback(async (collectionId: string) => {
    try {
      await postmanConverterApi.deleteCollection(collectionId);
      
      // Remove from collections state
      setCollections(prevCollections => 
        prevCollections.filter(c => c.id !== collectionId)
      );
      
      // Clear selected item if it was in this collection
      if (selectedItemId) {
        const collection = collections.find(c => c.id === collectionId);
        if (collection && collection.requests[selectedItemId]) {
          setSelectedItemId(null);
        }
      }
      
      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi, collections, selectedItemId]);

  // Handle import collection
  const handleImportCollection = useCallback(async (apiCollection: ApiCollection) => {
    // Convert ApiCollection to Postman collection format
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] // We would need to convert the folders and requests structure here
    };

    try {
      // Save to database
      const newCollection = await postmanConverterApi.createCollection({
        name: apiCollection.name,
        description: apiCollection.description,
        content: JSON.stringify(postmanCollection)
      });
      
      // Convert back to ApiCollection format
      const savedApiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, savedApiCollection]);

      return { success: true, collection: savedApiCollection };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  return {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
  };
};
