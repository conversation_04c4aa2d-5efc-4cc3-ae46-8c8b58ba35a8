import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
  StructuredMetadataTable,
  InfoCard,
  Link,
} from '@backstage/core-components';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import {
  Button,
  Grid,
  Typography,
  Tabs,
  Tab,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@material-ui/core';
import { useAsync } from 'react-use';
import { postmanConverterApiRef } from '../../api';
import { Collection, CollectionVersion } from '../../types';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import HistoryIcon from '@material-ui/icons/History';
import { useNavigate } from 'react-router-dom';
import { Alert } from '@material-ui/lab';
import { CodeSnippet } from '@backstage/core-components';

interface TabPanelProps {
  children?: React.ReactNode;
  index: any;
  value: any;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box p={3}>{children}</Box>}
    </div>
  );
}

export const CollectionDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  const [tabValue, setTabValue] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { value: collection, loading, error, reload } = useAsync(async () => {
    if (!id) return undefined;
    try {
      return await postmanConverterApi.getCollectionById(id);
    } catch (e) {
      errorApi.post(e);
      throw e;
    }
  }, [id]);

  const { value: versions, loading: versionsLoading } = useAsync(async () => {
    if (!id) return [];
    try {
      return await postmanConverterApi.getCollectionVersions(id);
    } catch (e) {
      errorApi.post(e);
      return [];
    }
  }, [id]);

  const handleTabChange = (_: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditClick = () => {
    if (id) {
      navigate(`/postman-converter/${id}/edit`);
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!id) return;
    try {
      await postmanConverterApi.deleteCollection(id);
      setIsDeleteDialogOpen(false);
      navigate('/postman-converter');
    } catch (e) {
      errorApi.post(e);
    }
  };

  const handleRollback = async (versionNumber: number) => {
    if (!id) return;
    try {
      await postmanConverterApi.rollbackToVersion(id, versionNumber);
      reload();
    } catch (e) {
      errorApi.post(e);
    }
  };

  if (loading) {
    return <Progress />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  if (!collection) {
    return <Alert severity="error">Collection not found</Alert>;
  }

  const metadata = {
    'Created': new Date(collection.created_at).toLocaleString(),
    'Updated': new Date(collection.updated_at).toLocaleString(),
    'Owner': collection.owner_id,
    'Current Version': collection.version || 'N/A',
  };

  return (
    <Content>
      <ContentHeader title={collection.name}>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<EditIcon />}
          onClick={handleEditClick}
        >
          Edit
        </Button>
        <Button
          variant="outlined"
          color="secondary"
          startIcon={<DeleteIcon />}
          onClick={handleDeleteClick}
        >
          Delete
        </Button>
        <SupportButton>
          View and manage your Postman collection
        </SupportButton>
      </ContentHeader>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard>
            <Typography variant="body1" paragraph>
              {collection.description || 'No description provided.'}
            </Typography>
            <StructuredMetadataTable metadata={metadata} />
          </InfoCard>
        </Grid>

        <Grid item xs={12}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Collection Content" />
            <Tab label="Version History" icon={<HistoryIcon />} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <InfoCard title="Collection Content">
              {collection.content ? (
                <>
                  <Typography variant="subtitle1" gutterBottom>
                    {(() => {
                      try {
                        const content = JSON.parse(collection.content);
                        // Display collection info based on format
                        if (content.info && content.info.name) {
                          return (
                            <>
                              <strong>Collection Name:</strong> {content.info.name}
                              {content.info.description && (
                                <Typography variant="body2" paragraph>
                                  <strong>Description:</strong> {content.info.description}
                                </Typography>
                              )}
                              {content.item && (
                                <Typography variant="body2">
                                  <strong>Endpoints:</strong> {content.item.length}
                                </Typography>
                              )}
                            </>
                          );
                        } else if (content.name) {
                          // Older format
                          return (
                            <>
                              <strong>Collection Name:</strong> {content.name}
                              {content.description && (
                                <Typography variant="body2" paragraph>
                                  <strong>Description:</strong> {content.description}
                                </Typography>
                              )}
                            </>
                          );
                        }
                        return "Postman Collection";
                      } catch (e) {
                        return "Postman Collection";
                      }
                    })()}
                  </Typography>
                  <CodeSnippet
                    text={collection.content}
                    language="json"
                    showCopyCodeButton
                  />
                </>
              ) : (
                <Typography variant="body1">No content available</Typography>
              )}
            </InfoCard>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {versionsLoading ? (
              <Progress />
            ) : (
              <InfoCard title="Version History">
                {versions && versions.length > 0 ? (
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr>
                        <th>Version</th>
                        <th>Created</th>
                        <th>User</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {versions.map((version: CollectionVersion) => (
                        <tr key={version.id}>
                          <td>v{version.version}</td>
                          <td>{new Date(version.created_at).toLocaleString()}</td>
                          <td>{version.user_id}</td>
                          <td>
                            <Button
                              size="small"
                              color="primary"
                              onClick={() => handleRollback(version.version)}
                              disabled={version.version === collection.version}
                            >
                              Rollback to this version
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <Typography>No version history available</Typography>
                )}
              </InfoCard>
            )}
          </TabPanel>
        </Grid>
      </Grid>



      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onClose={() => setIsDeleteDialogOpen(false)}>
        <DialogTitle>Delete Collection</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the collection "{collection.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="secondary">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Content>
  );
};
