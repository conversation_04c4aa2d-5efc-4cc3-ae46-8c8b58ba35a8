import React from 'react';
import {
  Table,
  TableColumn,
  Progress,
  EmptyState,
  ErrorPanel,
} from '@backstage/core-components';
import { useApi, configApiRef, errorApiRef } from '@backstage/core-plugin-api';
import { Button, Typography } from '@material-ui/core';
import { Link as RouterLink } from 'react-router-dom';
import { postmanConverterApiRef } from '../../api';
import { Collection } from '../../types';
import { useAsync } from 'react-use';
import AddIcon from '@material-ui/icons/Add';

export const CollectionListPage = () => {
  const configApi = useApi(configApiRef);
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  const { value: collections, loading, error } = useAsync(async () => {
    try {
      return await postmanConverterApi.getCollections();
    } catch (e) {
      errorApi.post(e);
      throw e;
    }
  }, []);

  const columns: TableColumn<Collection>[] = [
    {
      title: 'Name',
      field: 'name',
      highlight: true,
      render: (row: Collection) => (
        <RouterLink to={`/postman-converter/${row.id}`}>{row.name}</RouterLink>
      ),
    },
    {
      title: 'Description',
      field: 'description',
    },
    {
      title: 'Created',
      field: 'created_at',
      render: (row: Collection) => new Date(row.created_at).toLocaleString(),
    },
    {
      title: 'Updated',
      field: 'updated_at',
      render: (row: Collection) => new Date(row.updated_at).toLocaleString(),
    },
  ];

  if (loading) {
    return <Progress />;
  }

  if (error) {
    return <ErrorPanel error={error} />;
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
        <Button
          component={RouterLink}
          to="/postman-converter/new"
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
        >
          Add Collection
        </Button>
      </div>

      {collections && collections.length > 0 ? (
        <Table
          options={{ paging: true, search: true }}
          data={collections}
          columns={columns}
          title="Collections"
        />
      ) : (
        <EmptyState
          missing="data"
          title="No collections"
          description="You haven't created any collections yet."
          action={
            <Button
              variant="contained"
              color="primary"
              component={RouterLink}
              to="/postman-converter/new"
            >
              Create Collection
            </Button>
          }
        />
      )}
    </div>
  );
};
