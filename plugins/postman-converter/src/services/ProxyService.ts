import { createApiRef, <PERSON><PERSON><PERSON> } from '@backstage/core-plugin-api';

/**
 * API for proxying HTTP requests to avoid CORS issues
 */
export interface ProxyServiceApi {
  /**
   * The discovery API used to get the proxy URL
   */
  readonly discoveryApi: DiscoveryApi;

  /**
   * Send a proxied HTTP request
   *
   * @param url - The URL to send the request to
   * @param options - The request options
   * @param useProxy - Whether to use the Backstage proxy (default: auto-detect)
   * @returns The response from the proxied request
   */
  fetch(url: string, options?: RequestInit, useProxy?: boolean): Promise<Response>;

  /**
   * Check if a URL is internal (should use proxy) or external
   *
   * @param url - The URL to check
   * @returns True if the URL is internal, false if external
   */
  isInternalUrl(url: string): boolean;
}

/**
 * API Reference for the ProxyServiceApi
 */
export const proxyServiceApiRef = createApiRef<ProxyServiceApi>({
  id: 'plugin.postman-converter.proxy',
});

/**
 * Implementation of the ProxyServiceApi
 */
export class ProxyService implements ProxyServiceApi {
  readonly discoveryApi: DiscoveryApi;
  private readonly internalDomains: string[];

  constructor(options: { discoveryApi: DiscoveryApi }) {
    this.discoveryApi = options.discoveryApi;

    // List of domains that should be considered internal
    // These will be routed through the proxy
    this.internalDomains = [
      'localhost',
      '127.0.0.1',
      window.location.hostname,
    ];
  }

  /**
   * Check if a URL is internal (should use proxy) or external
   *
   * @param url - The URL to check
   * @returns True if the URL is internal, false if external
   */
  isInternalUrl(url: string): boolean {
    try {
      const targetUrl = new URL(url);

      // Check if the URL's hostname is in the list of internal domains
      return this.internalDomains.some(domain =>
        targetUrl.hostname === domain ||
        targetUrl.hostname.endsWith(`.${domain}`)
      );
    } catch (error) {
      // If the URL is invalid, assume it's internal (relative path)
      return true;
    }
  }

  /**
   * Send a request through the Backstage proxy to avoid CORS issues
   *
   * @param url - The URL to send the request to
   * @param options - The request options
   * @param useProxy - Whether to use the Backstage proxy (default: true)
   * @returns The response from the request
   */
  async fetch(url: string, options: RequestInit = {}, useProxy: boolean = true): Promise<Response> {
    // Always use the proxy to avoid CORS issues
    if (useProxy) {
      // Get the proxy URL from the discovery API
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');

      try {
        // Create a URL object to extract the target URL parts
        const targetUrl = new URL(url);

        // Construct the proxied URL
        // Format: /proxy/{target-host}/{target-path}
        const proxiedUrl = `${proxyUrl}/${targetUrl.host}${targetUrl.pathname}${targetUrl.search}`;

        // Forward the request to the proxy
        return fetch(proxiedUrl, {
          ...options,
          headers: {
            ...options.headers,
            // Add headers to indicate the original URL scheme (http/https)
            'X-Requested-With': 'XMLHttpRequest',
            'X-Original-URL': url,
          },
        });
      } catch (error) {
        // If URL parsing fails, try to handle it as a relative path
        const proxiedUrl = `${proxyUrl}/${url.replace(/^\//, '')}`;

        return fetch(proxiedUrl, {
          ...options,
          headers: {
            ...options.headers,
            'X-Requested-With': 'XMLHttpRequest',
          },
        });
      }
    } else {
      // This branch is kept for flexibility but not used by default

      return fetch(url, options);
    }
  }
}
