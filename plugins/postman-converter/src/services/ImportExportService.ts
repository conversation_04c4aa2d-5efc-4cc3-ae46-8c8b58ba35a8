import {
  ApiCollection,
  ApiEnvironment,
  ApiFolder,
  ApiRequest,
} from '../types';

/**
 * Service for importing and exporting collections and environments
 */
export class ImportExportService {
  /**
   * Convert a Postman collection to the internal ApiCollection format
   * 
   * @param postmanCollection - The Postman collection JSON
   * @returns The converted ApiCollection
   */
  static importPostmanCollection(postmanCollection: any): ApiCollection {
    // Generate a unique ID for the collection
    const collectionId = `col_${Date.now()}`;
    
    // Initialize the collection structure
    const collection: ApiCollection = {
      id: collectionId,
      name: postmanCollection.info?.name || 'Imported Collection',
      description: postmanCollection.info?.description || '',
      folders: [],
      requests: {},
      environments: [],
    };
    
    // Process the items (requests and folders)
    if (postmanCollection.item && Array.isArray(postmanCollection.item)) {
      this.processPostmanItems(postmanCollection.item, collection, null);
    }
    
    return collection;
  }
  
  /**
   * Process Postman collection items (requests and folders)
   * 
   * @param items - The Postman collection items
   * @param collection - The target ApiCollection
   * @param parentFolderId - The parent folder ID (if any)
   */
  private static processPostmanItems(
    items: any[],
    collection: ApiCollection,
    parentFolderId: string | null
  ): void {
    items.forEach(item => {
      if (item.item && Array.isArray(item.item)) {
        // This is a folder
        const folderId = `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        const folder: ApiFolder = {
          id: folderId,
          name: item.name || 'Unnamed Folder',
          parentId: parentFolderId || undefined,
          requests: [],
          folders: [],
        };
        
        // Process nested items
        this.processPostmanItems(item.item, collection, folderId);
        
        // Add the folder to the collection or parent folder
        if (parentFolderId) {
          // Find the parent folder and add this folder to it
          const parentFolder = this.findFolderById(collection.folders, parentFolderId);
          if (parentFolder) {
            parentFolder.folders.push(folder);
          }
        } else {
          // Add to the top level folders
          collection.folders.push(folder);
        }
      } else {
        // This is a request
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        // Convert the Postman request to our format
        const request = this.convertPostmanRequest(item, requestId);
        
        // Add the request to the collection
        collection.requests[requestId] = request;
        
        // Add the request ID to the parent folder if it exists
        if (parentFolderId) {
          const parentFolder = this.findFolderById(collection.folders, parentFolderId);
          if (parentFolder) {
            parentFolder.requests.push(requestId);
          }
        }
      }
    });
  }
  
  /**
   * Find a folder by its ID in the folder hierarchy
   * 
   * @param folders - The folders to search
   * @param folderId - The folder ID to find
   * @returns The found folder or undefined
   */
  private static findFolderById(folders: ApiFolder[], folderId: string): ApiFolder | undefined {
    for (const folder of folders) {
      if (folder.id === folderId) {
        return folder;
      }
      
      if (folder.folders.length > 0) {
        const nestedResult = this.findFolderById(folder.folders, folderId);
        if (nestedResult) {
          return nestedResult;
        }
      }
    }
    
    return undefined;
  }
  
  /**
   * Convert a Postman request to the internal ApiRequest format
   * 
   * @param postmanRequest - The Postman request
   * @param requestId - The ID to assign to the request
   * @returns The converted ApiRequest
   */
  private static convertPostmanRequest(postmanRequest: any, requestId: string): ApiRequest {
    // Extract the request method
    const method = (postmanRequest.request?.method || 'GET').toUpperCase();
    
    // Extract the URL
    let url = '';
    if (typeof postmanRequest.request?.url === 'string') {
      url = postmanRequest.request.url;
    } else if (postmanRequest.request?.url?.raw) {
      url = postmanRequest.request.url.raw;
    }
    
    // Extract headers
    const headers = (postmanRequest.request?.header || []).map((header: any) => ({
      key: header.key || '',
      value: header.value || '',
      enabled: header.disabled !== true,
    }));
    
    // Extract query parameters
    const params = (postmanRequest.request?.url?.query || []).map((param: any) => ({
      key: param.key || '',
      value: param.value || '',
      enabled: param.disabled !== true,
    }));
    
    // Extract body
    const body: ApiRequest['body'] = { mode: 'none' };
    
    if (postmanRequest.request?.body) {
      const postmanBody = postmanRequest.request.body;
      
      if (postmanBody.mode === 'raw') {
        body.mode = 'raw';
        body.raw = postmanBody.raw || '';
      } else if (postmanBody.mode === 'formdata') {
        body.mode = 'form-data';
        body.formData = (postmanBody.formdata || []).map((item: any) => ({
          key: item.key || '',
          value: item.value || '',
          type: item.type || 'text',
        }));
      } else if (postmanBody.mode === 'urlencoded') {
        body.mode = 'urlencoded';
        body.urlencoded = (postmanBody.urlencoded || []).map((item: any) => ({
          key: item.key || '',
          value: item.value || '',
        }));
      }
    }
    
    return {
      id: requestId,
      name: postmanRequest.name || 'Unnamed Request',
      method: method as any,
      url,
      headers,
      params,
      body,
    };
  }
  
  /**
   * Export a collection to Postman format
   * 
   * @param collection - The collection to export
   * @returns The Postman collection JSON
   */
  static exportToPostmanCollection(collection: ApiCollection): any {
    const postmanCollection = {
      info: {
        _postman_id: collection.id,
        name: collection.name,
        description: collection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      },
      item: this.convertFoldersAndRequestsToPostmanItems(collection),
    };
    
    return postmanCollection;
  }
  
  /**
   * Convert folders and requests to Postman items
   * 
   * @param collection - The collection to convert
   * @returns The Postman items
   */
  private static convertFoldersAndRequestsToPostmanItems(collection: ApiCollection): any[] {
    const items: any[] = [];
    
    // Convert top-level folders
    collection.folders.forEach(folder => {
      items.push(this.convertFolderToPostmanItem(folder, collection));
    });
    
    // Find requests that are not in any folder
    const requestsInFolders = new Set<string>();
    const getAllRequestIds = (folders: ApiFolder[]) => {
      folders.forEach(folder => {
        folder.requests.forEach(reqId => requestsInFolders.add(reqId));
        getAllRequestIds(folder.folders);
      });
    };
    
    getAllRequestIds(collection.folders);
    
    // Add requests that are not in any folder
    Object.keys(collection.requests).forEach(requestId => {
      if (!requestsInFolders.has(requestId)) {
        const request = collection.requests[requestId];
        items.push(this.convertRequestToPostmanItem(request));
      }
    });
    
    return items;
  }
  
  /**
   * Convert a folder to a Postman item
   * 
   * @param folder - The folder to convert
   * @param collection - The parent collection
   * @returns The Postman item
   */
  private static convertFolderToPostmanItem(folder: ApiFolder, collection: ApiCollection): any {
    const item: any = {
      name: folder.name,
      item: [],
    };
    
    // Add requests in this folder
    folder.requests.forEach(requestId => {
      const request = collection.requests[requestId];
      if (request) {
        item.item.push(this.convertRequestToPostmanItem(request));
      }
    });
    
    // Add subfolders
    folder.folders.forEach(subfolder => {
      item.item.push(this.convertFolderToPostmanItem(subfolder, collection));
    });
    
    return item;
  }
  
  /**
   * Convert a request to a Postman item
   * 
   * @param request - The request to convert
   * @returns The Postman item
   */
  private static convertRequestToPostmanItem(request: ApiRequest): any {
    const item: any = {
      name: request.name,
      request: {
        method: request.method,
        header: request.headers.map(header => ({
          key: header.key,
          value: header.value,
          disabled: !header.enabled,
        })),
        url: {
          raw: request.url,
          query: request.params.map(param => ({
            key: param.key,
            value: param.value,
            disabled: !param.enabled,
          })),
        },
      },
    };
    
    // Add body if it exists
    if (request.body.mode !== 'none') {
      item.request.body = {
        mode: request.body.mode,
      };
      
      if (request.body.mode === 'raw' && request.body.raw) {
        item.request.body.raw = request.body.raw;
      } else if (request.body.mode === 'form-data' && request.body.formData) {
        item.request.body.formdata = request.body.formData.map(item => ({
          key: item.key,
          value: item.value,
          type: item.type,
        }));
      } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
        item.request.body.urlencoded = request.body.urlencoded.map(item => ({
          key: item.key,
          value: item.value,
        }));
      }
    }
    
    return item;
  }
  
  /**
   * Import a Postman environment
   * 
   * @param postmanEnvironment - The Postman environment JSON
   * @returns The converted ApiEnvironment
   */
  static importPostmanEnvironment(postmanEnvironment: any): ApiEnvironment {
    const environment: ApiEnvironment = {
      id: `env_${Date.now()}`,
      name: postmanEnvironment.name || 'Imported Environment',
      variables: [],
    };
    
    // Process variables
    if (postmanEnvironment.values && Array.isArray(postmanEnvironment.values)) {
      environment.variables = postmanEnvironment.values.map((variable: any) => ({
        key: variable.key || '',
        value: variable.value || '',
        enabled: variable.enabled !== false,
      }));
    }
    
    return environment;
  }
  
  /**
   * Export an environment to Postman format
   * 
   * @param environment - The environment to export
   * @returns The Postman environment JSON
   */
  static exportToPostmanEnvironment(environment: ApiEnvironment): any {
    return {
      id: environment.id,
      name: environment.name,
      values: environment.variables.map(variable => ({
        key: variable.key,
        value: variable.value,
        enabled: variable.enabled,
      })),
      _postman_variable_scope: 'environment',
      _postman_exported_at: new Date().toISOString(),
      _postman_exported_using: 'Backstage Postman Converter',
    };
  }
}
