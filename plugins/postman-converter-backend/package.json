{"name": "@internal/plugin-postman-converter-backend", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-defaults": "^0.9.0", "@backstage/backend-plugin-api": "^1.3.0", "@backstage/catalog-client": "^1.9.1", "@backstage/errors": "^1.2.7", "@backstage/plugin-catalog-node": "^1.16.3", "express": "^4.17.1", "express-promise-router": "^4.1.0", "knex": "^3.0.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@backstage/backend-test-utils": "^1.4.0", "@backstage/cli": "^0.32.0", "@types/express": "^4.17.6", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.8", "supertest": "^6.2.4"}, "files": ["dist"]}