import { HttpAuthService } from '@backstage/backend-plugin-api';
import { InputError, NotFoundError, NotAllowedError } from '@backstage/errors';
import { z } from 'zod';
import express from 'express';
import Router from 'express-promise-router';
import { DatabaseHandler } from './database/models';
import { Logger } from 'winston';
import { Knex } from 'knex';

export async function createRouter({
  httpAuth,
  database,
  logger,
}: {
  httpAuth: HttpAuthService;
  database: Knex;
  logger: Logger;
}): Promise<express.Router> {
  const router = Router();
  router.use(express.json());

  const dbHandler = new DatabaseHandler(database, logger);

  // Ensure tables exist
  await dbHandler.createTables();

  // Schema validation
  const postmanCollectionSchema = z.object({
    info: z.object({
      _postman_id: z.string().optional(),
      name: z.string(),
      schema: z.string().optional(),
      description: z.string().optional(),
    }),
    item: z.array(z.any()),
  }).or(
    // Support for older Postman collection format
    z.object({
      id: z.string().optional(),
      name: z.string(),
      description: z.string().optional(),
      requests: z.array(z.any()).optional(),
      folders: z.array(z.any()).optional(),
    })
  );

  const collectionSchema = z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    content: z.string().refine(
      (val) => {
        try {
          const parsed = JSON.parse(val);
          return postmanCollectionSchema.safeParse(parsed).success;
        } catch (e) {
          return false;
        }
      },
      {
        message: "Content must be a valid Postman collection JSON",
      }
    ),
  });

  const collectionUpdateSchema = z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    content: z.string().optional().refine(
      (val) => {
        if (!val) return true; // Skip validation if not provided
        try {
          const parsed = JSON.parse(val);
          return postmanCollectionSchema.safeParse(parsed).success;
        } catch (e) {
          return false;
        }
      },
      {
        message: "Content must be a valid Postman collection JSON",
      }
    ),
  });

  // Helper function to check if user is admin
  const isUserAdmin = (userId: string): boolean => {
    // In a real implementation, this would check against a database or other source
    // For now, we'll use a simple check based on user ID
    return userId.includes('admin');
  };

  // GET /api/collections - List collections (user: own, admin: all)
  router.get('/collections', async (req, res) => {
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      // Use a default user ID if credentials or claims are not available
      const userId = credentials?.claims?.sub || 'guest';
      const admin = isUserAdmin(userId);

      const collections = await dbHandler.getCollections(userId, admin);
      res.json(collections);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
      // Fallback to guest user if authentication fails
      const collections = await dbHandler.getCollections('guest', false);
      res.json(collections);
    }
  });

  // POST /api/collections - Upload new collection
  router.post('/collections', async (req, res) => {
    let userId = 'guest';
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const parsed = collectionSchema.safeParse(req.body);
    if (!parsed.success) {
      throw new InputError(parsed.error.toString());
    }

    const { name, description = '', content } = parsed.data;
    const collection = await dbHandler.createCollection(name, description, userId, content);

    res.status(201).json(collection);
  });

  // GET /api/collections/:id - View collection details
  router.get('/collections/:id', async (req, res) => {
    let userId = 'guest';
    let admin = false;
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
      admin = isUserAdmin(userId);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const collectionId = req.params.id;

    const collection = await dbHandler.getCollectionById(collectionId, userId, admin);
    if (!collection) {
      throw new NotFoundError(`Collection with id ${collectionId} not found`);
    }

    // Get the latest version
    const versions = await dbHandler.getCollectionVersions(collectionId, userId, admin);
    const latestVersion = versions[0]; // Versions are ordered by version desc

    res.json({
      ...collection,
      content: latestVersion ? latestVersion.content : null,
      version: latestVersion ? latestVersion.version : null,
    });
  });

  // PATCH /api/collections/:id - Edit collection
  router.patch('/collections/:id', async (req, res) => {
    let userId = 'guest';
    let admin = false;
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
      admin = isUserAdmin(userId);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const collectionId = req.params.id;

    const parsed = collectionUpdateSchema.safeParse(req.body);
    if (!parsed.success) {
      throw new InputError(parsed.error.toString());
    }

    const collection = await dbHandler.updateCollection(
      collectionId,
      userId,
      admin,
      parsed.data,
    );

    if (!collection) {
      throw new NotFoundError(`Collection with id ${collectionId} not found or you don't have permission to update it`);
    }

    // Get the latest version
    const versions = await dbHandler.getCollectionVersions(collectionId, userId, admin);
    const latestVersion = versions[0]; // Versions are ordered by version desc

    res.json({
      ...collection,
      content: latestVersion ? latestVersion.content : null,
      version: latestVersion ? latestVersion.version : null,
    });
  });

  // DELETE /api/collections/:id - Delete collection
  router.delete('/collections/:id', async (req, res) => {
    let userId = 'guest';
    let admin = false;
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
      admin = isUserAdmin(userId);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const collectionId = req.params.id;

    const success = await dbHandler.deleteCollection(collectionId, userId, admin);
    if (!success) {
      throw new NotFoundError(`Collection with id ${collectionId} not found or you don't have permission to delete it`);
    }

    res.status(204).end();
  });

  // GET /api/collections/:id/history - View version history
  router.get('/collections/:id/history', async (req, res) => {
    let userId = 'guest';
    let admin = false;
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
      admin = isUserAdmin(userId);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const collectionId = req.params.id;

    const collection = await dbHandler.getCollectionById(collectionId, userId, admin);
    if (!collection) {
      throw new NotFoundError(`Collection with id ${collectionId} not found`);
    }

    const versions = await dbHandler.getCollectionVersions(collectionId, userId, admin);
    res.json(versions);
  });

  // POST /api/collections/:id/rollback/:versionId - Rollback to a specific version
  router.post('/collections/:id/rollback/:versionNumber', async (req, res) => {
    let userId = 'guest';
    let admin = false;
    try {
      const credentials = await httpAuth.credentials(req, { allow: ['user'] });
      userId = credentials?.claims?.sub || 'guest';
      admin = isUserAdmin(userId);
    } catch (error) {
      logger.warn('Error authenticating user', { error });
    }

    const collectionId = req.params.id;
    const versionNumber = parseInt(req.params.versionNumber, 10);

    if (isNaN(versionNumber)) {
      throw new InputError('Version number must be a valid integer');
    }

    const version = await dbHandler.rollbackToVersion(collectionId, versionNumber, userId, admin);
    if (!version) {
      throw new NotFoundError(`Collection with id ${collectionId} or version ${versionNumber} not found`);
    }

    res.json(version);
  });

  return router;
}
